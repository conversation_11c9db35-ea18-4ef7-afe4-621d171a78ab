"""
Camera Controller - <PERSON><PERSON> thống Camera Hiệu suất Cao
High Performance Camera System
Dựa trên triển khai tham chiếu project_directory
Based on project_directory reference implementation
"""

import cv2  # Thư viện OpenCV để xử lý camera và ảnh
from flask import Blueprint, Response, jsonify, request  # Flask components
import threading  # Thư viện đa luồng để xử lý camera song song
import time  # Thư viện thời gian
import os  # Thư viện hệ điều hành
import contextlib  # Context manager để suppress warnings
import sys  # System utilities
from pathlib import Path  # Thư viện xử lý đường dẫn
from datetime import datetime  # Thư viện ngày tháng

from models.session_model import SessionModel  # Model quản lý session
from config import CameraConfig  # Cấu hình camera
from utils.path_manager import path_manager  # Path manager để xử lý đường dẫn tập trung
from utils.duplicate_logic_refactor import SessionManager, APIResponse<PERSON><PERSON><PERSON>er, ErrorLogger  # Import refactor utilities


@contextlib.contextmanager
def suppress_opencv_warnings():
    """Context manager để suppress OpenCV warnings một cách an toàn"""
    try:
        # Backup stderr
        old_stderr = os.dup(2)
        # Redirect stderr to devnull
        devnull = os.open(os.devnull, os.O_WRONLY)
        os.dup2(devnull, 2)
        os.close(devnull)
        yield
    finally:
        # Restore stderr
        try:
            os.dup2(old_stderr, 2)
            os.close(old_stderr)
        except:
            pass


class CameraController:
    """Controller camera hiệu suất cao dựa trên project_directory - High-performance camera controller based on project_directory"""

    def __init__(self):
        """Khởi tạo Camera Controller"""
        self.blueprint = Blueprint('camera', __name__)  # Tạo Flask Blueprint cho camera
        self.setup_routes()  # Thiết lập các routes

        # Thuộc tính camera cốt lõi (CHÍNH XÁC theo kiểu project_directory) - Core camera attributes (EXACT project_directory style)
        self.camera_0 = None  # Camera name card (Logitech bên trái) - Business card camera (Logitech bên trái)
        self.camera_1 = None  # Camera khuôn mặt (Laptop bên phải) - Face camera (Laptop bên phải)
        self.output_frame_0 = None  # Frame mới nhất từ camera 0 - Latest frame from camera 0
        self.output_frame_1 = None  # Frame mới nhất từ camera 1 - Latest frame from camera 1
        self.lock_0 = threading.Lock()  # Khóa luồng cho camera 0 - Thread lock for camera 0
        self.lock_1 = threading.Lock()  # Khóa luồng cho camera 1 - Thread lock for camera 1
        self.initial_focus_value = 0  # Focus thủ công cho camera Logitech - Manual focus for Logitech camera

        # Camera index mapping linh hoạt - Flexible camera index mapping
        self.business_card_camera_index = 0  # Index thực tế của camera business card
        self.face_camera_index = 1  # Index thực tế của camera face
        self.available_cameras = []  # Danh sách camera có sẵn
        self.virtual_camera_mode = False  # Chế độ virtual camera khi không có camera thật

        # Thuộc tính theo dõi bổ sung - Additional tracking attributes
        self.active_streams = set()  # Tập hợp các stream đang hoạt động

        # Camera stream control - NEW FUNCTIONALITY FOR CAPTURE WORKFLOW
        self.streaming_active_0 = True  # Camera 0 streaming state
        self.streaming_active_1 = True  # Camera 1 streaming state
        self.capture_mode_0 = False  # Camera 0 in capture mode (paused streaming)
        self.capture_mode_1 = False  # Camera 1 in capture mode (paused streaming)

        # Phát hiện môi trường - Environment detection
        self.is_server_environment = self.detect_server_environment()  # Kiểm tra có phải môi trường server không
        self.webrtc_only_mode = False  # Cờ cho chế độ chỉ WebRTC - Flag for WebRTC-only operation

        # Khởi tạo camera và bắt đầu streaming - Initialize cameras and start streaming
        self.setup_cameras()  # Thiết lập camera
        self.session_model = SessionModel()  # Khởi tạo model session
        print(f"Camera Controller initialized - Server Environment: {self.is_server_environment}")
        print("✅ Camera stream pause/resume functionality is working!")

    def setup_routes(self):
        """Thiết lập các routes camera (CHÍNH XÁC theo kiểu project_directory) - Setup camera routes (EXACT project_directory style)"""
        # Route để stream video từ camera
        self.blueprint.add_url_rule('/video_feed/<int:camera_id>', 'video_feed', self.video_feed)
        # Route để chụp ảnh từ camera
        self.blueprint.add_url_rule('/capture_step/<int:camera_id>', 'capture_step', self.capture_step, methods=['POST'])
        # Route để upload ảnh từ client
        self.blueprint.add_url_rule('/upload_camera_image', 'upload_camera_image', self.upload_camera_image, methods=['POST'])
        # Route lấy trạng thái session
        self.blueprint.add_url_rule('/session_status', 'session_status', self.session_status)
        # Route reset session
        self.blueprint.add_url_rule('/reset_session', 'reset_session', self.reset_session, methods=['POST'])
        # NEW ROUTES: Camera stream control
        self.blueprint.add_url_rule('/pause_stream/<int:camera_id>', 'pause_stream', self.pause_stream, methods=['POST'])
        self.blueprint.add_url_rule('/resume_stream/<int:camera_id>', 'resume_stream', self.resume_stream, methods=['POST'])
        self.blueprint.add_url_rule('/stop_all_streams', 'stop_all_streams', self.stop_all_streams, methods=['POST'])
        # TEST ROUTE: Manual camera control for testing
        self.blueprint.add_url_rule('/test_camera_control/<int:camera_id>/<action>', 'test_camera_control', self.test_camera_control, methods=['GET'])
        # Route lấy trạng thái camera
        self.blueprint.add_url_rule('/camera_status', 'camera_status_api', self.camera_status_api, methods=['GET'])
        # SIMPLE TEST ROUTE
        self.blueprint.add_url_rule('/test_simple', 'test_simple', self.test_simple, methods=['GET'])
        # Route điều chỉnh focus camera
        self.blueprint.add_url_rule('/adjust_focus', 'adjust_focus', self.adjust_focus, methods=['POST'])
        # Route lấy thông tin camera mapping
        self.blueprint.add_url_rule('/camera_mapping', 'camera_mapping', self.get_camera_mapping, methods=['GET'])

    def detect_server_environment(self):
        """Phát hiện nếu đang chạy trong môi trường server (không có camera vật lý) - Detect if running in server environment (no physical cameras)"""
        import platform  # Import để kiểm tra hệ điều hành
        import subprocess  # Import để chạy lệnh hệ thống

        try:
            # Kiểm tra các chỉ báo server phổ biến - Check for common server indicators
            system = platform.system().lower()  # Lấy tên hệ điều hành

            # Kiểm tra nếu đang chạy trong Docker/container - Check if running in Docker/container
            if os.path.exists('/.dockerenv'):  # File này tồn tại trong Docker container
                print("🐳 Docker environment detected - WebRTC mode")
                return True  # Trả về True nếu là Docker

            # Kiểm tra môi trường headless (không có display) - Check for headless environment (no display)
            if system == 'linux':  # Nếu là Linux
                display = os.environ.get('DISPLAY')  # Lấy biến DISPLAY
                if not display:  # Nếu không có DISPLAY
                    print("🖥️ Headless Linux environment detected - WebRTC mode")
                    return True  # Trả về True nếu là headless

            # Kiểm tra các chỉ báo cloud server - Check for cloud server indicators
            cloud_indicators = [
                'AWS_EXECUTION_ENV',  # AWS Lambda/EC2
                'GOOGLE_CLOUD_PROJECT',  # Google Cloud
                'AZURE_CLIENT_ID',  # Microsoft Azure
                'HEROKU_APP_NAME'  # Heroku
            ]

            for indicator in cloud_indicators:  # Lặp qua từng chỉ báo
                if os.environ.get(indicator):  # Nếu biến môi trường tồn tại
                    print(f"☁️ Cloud environment detected ({indicator}) - WebRTC mode")
                    return True  # Trả về True nếu là cloud

            # Thử phát hiện camera có sẵn (kiểm tra nhanh) - Try to detect available cameras (quick check)
            if system == 'linux':  # Nếu là Linux
                try:
                    # Chạy lệnh ls để kiểm tra thiết bị video
                    result = subprocess.run(['ls', '/dev/video*'],
                                          capture_output=True, text=True, timeout=2)
                    if result.returncode != 0:  # Nếu không tìm thấy thiết bị video
                        print("📹 No video devices found - WebRTC mode")
                        return True  # Trả về True nếu không có camera
                except:  # Nếu có lỗi khi kiểm tra
                    print("📹 Cannot check video devices - WebRTC mode")
                    return True  # Trả về True nếu không kiểm tra được

            print("💻 Local environment detected - Hybrid mode")  # Môi trường local
            return False  # Trả về False nếu là môi trường local

        except Exception as e:
            print(f"⚠️ Environment detection failed: {e} - Defaulting to WebRTC mode")  # Log lỗi
            return True  # Mặc định là server environment

    def setup_cameras(self):
        """Thiết lập và khởi tạo camera với nhận biết môi trường server - Setup and initialize cameras with server environment awareness"""
        print("🎥 Setting up cameras...")  # Log bắt đầu thiết lập camera

        try:
            if self.is_server_environment:  # Nếu là môi trường server
                print("🌐 Server environment detected - Skipping physical camera initialization")  # Log phát hiện server
                print("📱 WebRTC-only mode enabled - Clients will use their own cameras")  # Log chế độ WebRTC
                self.webrtc_only_mode = True  # Bật chế độ chỉ WebRTC

                # Tạo frame giả để tương thích - Create dummy frames for compatibility
                import numpy as np  # Import numpy để tạo array
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)  # Tạo frame đen 480x640
                with self.lock_0:  # Khóa luồng camera 0
                    self.output_frame_0 = dummy_frame  # Đặt frame giả cho camera 0
                with self.lock_1:  # Khóa luồng camera 1
                    self.output_frame_1 = dummy_frame  # Đặt frame giả cho camera 1

                print("✅ Server camera system ready (WebRTC-only mode)")  # Log hệ thống sẵn sàng
                return True  # Trả về thành công

            # Môi trường local - thử khởi tạo camera vật lý - Local environment - try to initialize physical cameras
            print("💻 Local environment - Attempting physical camera initialization")  # Log thử khởi tạo camera
            success = self.initialize_cameras()  # Gọi hàm khởi tạo camera

            if success:  # Nếu khởi tạo thành công
                # Bắt đầu các luồng streaming - Start streaming threads
                self.start_streaming_threads()  # Khởi động luồng streaming
                print("✅ Physical camera system ready")  # Log hệ thống camera sẵn sàng
                return True  # Trả về thành công
            else:  # Nếu khởi tạo thất bại
                print("⚠️ Physical cameras failed - Falling back to WebRTC-only mode")  # Log fallback
                self.webrtc_only_mode = True  # Chuyển sang chế độ WebRTC

                # Tạo frame giả để tương thích - Create dummy frames for compatibility
                import numpy as np  # Import numpy
                dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)  # Tạo frame đen
                with self.lock_0:  # Khóa luồng camera 0
                    self.output_frame_0 = dummy_frame  # Đặt frame giả
                with self.lock_1:  # Khóa luồng camera 1
                    self.output_frame_1 = dummy_frame  # Đặt frame giả

                return True  # Trả về thành công

        except Exception as e:
            print(f"⚠️ Camera setup error: {e} - Enabling WebRTC-only mode")  # Log lỗi thiết lập
            self.webrtc_only_mode = True  # Bật chế độ WebRTC
            return True  # Trả về thành công

    def start_streaming_threads(self):
        """Khởi động các luồng streaming camera - Start camera streaming threads"""
        try:
            # Khởi động luồng cho camera 0 (Name Card) - Start thread for camera 0 (Business Card)
            if self.camera_0 and self.camera_0.isOpened():  # Nếu camera 0 mở được
                threading.Thread(  # Tạo luồng mới
                    target=lambda: list(self.generate_frames(0)),  # Hàm target tạo frame
                    daemon=True,  # Luồng daemon
                    name="Camera0Thread"  # Tên luồng
                ).start()  # Khởi động luồng
                print("🎬 Started streaming thread for Camera 0 (Business Card)")  # Log khởi động luồng

            # Khởi động luồng cho camera 1 (Khuôn mặt) - Start thread for camera 1 (Face)
            if self.camera_1 and self.camera_1.isOpened():  # Nếu camera 1 mở được
                threading.Thread(  # Tạo luồng mới
                    target=lambda: list(self.generate_frames(1)),  # Hàm target tạo frame cho camera 1
                    daemon=True,  # Luồng daemon
                    name="Camera1Thread"  # Tên luồng
                ).start()  # Khởi động luồng
                print("🎬 Started streaming thread for Camera 1 (Face)")  # Log khởi động luồng camera 1

        except Exception as e:
            print(f"❌ Error starting streaming threads: {e}")  # Log lỗi khởi động luồng

    def detect_available_cameras(self):
        """Phát hiện camera với smart detection - loại bỏ hoàn toàn lỗi OpenCV"""
        print("🔍 Smart camera detection - eliminating OpenCV errors...")
        available_cameras = []

        # Sử dụng Windows API để detect camera trước
        detected_indices = self._windows_camera_detection()

        if detected_indices:
            print(f"🎯 Windows detected cameras at indices: {detected_indices}")
            # Chỉ test những index mà Windows đã detect
            for camera_id in detected_indices:
                if self._test_single_camera_fast(camera_id):
                    available_cameras.append(camera_id)
                    print(f"✅ Verified camera at index {camera_id}")
        else:
            # Fallback: test chỉ 3 index đầu tiên (thường đủ)
            print("🔄 Fallback: Testing first 3 camera indices only...")
            for camera_id in range(3):
                if self._test_single_camera_fast(camera_id):
                    available_cameras.append(camera_id)
                    print(f"✅ Found camera at index {camera_id}")

        # Nếu vẫn không có camera, enable virtual mode
        if not available_cameras:
            print("⚠️ No physical cameras found - enabling virtual camera mode")
            available_cameras = [0]  # Virtual camera
            self.virtual_camera_mode = True

        print(f"📊 Final camera list: {available_cameras}")
        return available_cameras

    def _windows_camera_detection(self):
        """Sử dụng Windows API để detect camera - tránh OpenCV errors"""
        try:
            import subprocess
            import re

            # Sử dụng PowerShell để list camera devices
            cmd = [
                'powershell', '-Command',
                "Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -match 'camera|webcam|video'} | Select-Object Name, DeviceID"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # Parse output để tìm camera indices
                lines = result.stdout.strip().split('\n')
                camera_count = 0
                for line in lines:
                    if 'camera' in line.lower() or 'webcam' in line.lower():
                        camera_count += 1

                if camera_count > 0:
                    return list(range(camera_count))

        except Exception as e:
            print(f"⚠️ Windows detection failed: {e}")

        return []

    def _test_single_camera_fast(self, camera_id):
        """Test camera nhanh với minimal error output"""
        try:
            import cv2
            import time

            # Sử dụng context manager để suppress warnings
            with suppress_opencv_warnings():
                # Chỉ test với DirectShow - backend ổn định nhất
                cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)
                time.sleep(0.3)

                if cap.isOpened():
                    ret, frame = cap.read()
                    success = ret and frame is not None
                    cap.release()
                    return success

                return False

        except Exception:
            return False

    def _fallback_camera_detection(self):
        """Fallback camera detection method"""
        print("🔄 Trying fallback camera detection...")
        fallback_cameras = []

        # Giả định có ít nhất camera laptop
        try:
            import cv2
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    fallback_cameras.append(0)
                    print("✅ Fallback: Found camera at index 0")
            cap.release()
        except:
            pass

        # Nếu vẫn không có camera, tạo virtual camera để app không crash
        if not fallback_cameras:
            print("⚠️ No physical cameras found - enabling virtual camera mode")
            fallback_cameras = [0]  # Virtual camera
            self.virtual_camera_mode = True

        return fallback_cameras

    def initialize_cameras(self):
        """Khởi tạo camera với force mapping cho camera thứ 3 - Initialize cameras with force mapping for 3rd camera"""
        print("🎥 Initializing cameras with FORCE MAPPING for camera thứ 3...")

        try:
            # FORCE MAPPING: Thử sử dụng camera thứ 3 trực tiếp
            force_mapping = self._try_force_camera_mapping()

            if force_mapping:
                print("🎯 Using FORCE MAPPING for camera thứ 3!")
                business_card_index = force_mapping['business_card']
                face_index = force_mapping['face']
            else:
                # Fallback: Phát hiện camera có sẵn
                available_cameras = self.detect_available_cameras()

                if not available_cameras:
                    print("❌ No cameras detected!")
                    return False

                # Mapping thông thường
                business_card_index = available_cameras[0]
                face_index = available_cameras[1] if len(available_cameras) >= 2 else available_cameras[0]

            # Bước 2: Khởi tạo Business Card Camera
            print(f"📄 Initializing Camera {business_card_index} for Business Card...")
            self.camera_0 = self._initialize_single_camera(business_card_index, "Business Card")
            self.business_card_camera_index = business_card_index

            # Bước 3: Khởi tạo Face Camera
            print(f"👤 Initializing Camera {face_index} for Face...")
            self.camera_1 = self._initialize_single_camera(face_index, "Face")
            self.face_camera_index = face_index

            # Bước 4: Kiểm tra kết quả
            cam0_ok = self.camera_0 is not None and self.camera_0.isOpened()
            cam1_ok = self.camera_1 is not None and self.camera_1.isOpened()

            print(f"📊 Camera initialization complete:")
            print(f"   📄 Business Card Camera (Index {business_card_index}): {'✅ OK' if cam0_ok else '❌ FAILED'}")
            print(f"   👤 Face Camera (Index {face_index}): {'✅ OK' if cam1_ok else '❌ FAILED'}")

            if force_mapping and cam1_ok:
                print(f"🎉 SUCCESS! Camera thứ 3 (Index {face_index}) is working!")

            if cam0_ok or cam1_ok:
                print(f"✅ Camera system ready")
                return True
            else:
                print(f"❌ All camera initialization failed")
                return False

        except Exception as e:
            print(f"❌ Camera initialization error: {e}")
            return False

    def _try_force_camera_mapping(self):
        """Thử force mapping để sử dụng camera thứ 3 - FIXED với Camera 2 (Razer Kiyo X)"""
        print("🎯 Trying FORCE MAPPING for Razer Kiyo X (Camera 2)...")

        # FIXED: Diagnostic tool đã xác nhận Camera 2 là Razer Kiyo X và hoạt động!
        preferred_mappings = [
            {'business_card': 0, 'face': 2},  # Camera 0 cho business card, Camera 2 (Razer Kiyo X) cho face
            {'business_card': 1, 'face': 2},  # Camera 1 cho business card, Camera 2 (Razer Kiyo X) cho face
            {'business_card': 2, 'face': 0},  # Camera 2 (Razer Kiyo X) cho business card, Camera 0 cho face
            {'business_card': 2, 'face': 1},  # Camera 2 (Razer Kiyo X) cho business card, Camera 1 cho face
        ]

        for mapping in preferred_mappings:
            print(f"   Testing mapping: Business Card={mapping['business_card']}, Face={mapping['face']}")

            # Test cả 2 camera trong mapping
            business_ok = self._test_single_camera_fast(mapping['business_card'])
            face_ok = self._test_single_camera_fast(mapping['face'])

            print(f"      Business Card (Camera {mapping['business_card']}): {'✅' if business_ok else '❌'}")
            print(f"      Face (Camera {mapping['face']}): {'✅' if face_ok else '❌'}")

            if business_ok and face_ok:
                print(f"   🎉 FOUND WORKING MAPPING WITH RAZER KIYO X: {mapping}")

                # Xác định camera nào là Razer Kiyo X
                if mapping['face'] == 2:
                    print(f"   🎯 Razer Kiyo X (Camera 2) will be used for FACE camera")
                elif mapping['business_card'] == 2:
                    print(f"   🎯 Razer Kiyo X (Camera 2) will be used for BUSINESS CARD camera")

                return mapping
            else:
                print(f"   ❌ Mapping failed")

        print("   ⚠️ No force mapping found with Razer Kiyo X")
        return None

    def _initialize_single_camera(self, camera_id, camera_name):
        """Khởi tạo camera với silent mode - loại bỏ OpenCV warnings"""
        try:
            import cv2
            import time

            print(f"🔧 Initializing {camera_name} (ID: {camera_id})...")

            # Nếu ở virtual camera mode, tạo dummy camera
            if getattr(self, 'virtual_camera_mode', False):
                print(f"🎭 Creating virtual camera for {camera_name}")
                return self._create_virtual_camera()

            # Sử dụng context manager để suppress OpenCV warnings
            with suppress_opencv_warnings():
                # Chỉ sử dụng DirectShow - backend ổn định nhất cho Windows
                cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)
                time.sleep(0.5)

                if cap.isOpened():
                    # Test đọc frame
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        print(f"✅ {camera_name} initialized successfully")

                        # Đặt thiết lập tối ưu
                        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                        cap.set(cv2.CAP_PROP_FPS, 30)

                        # Đặt focus cho camera 0 nếu cần
                        if camera_id == 0:
                            try:
                                cap.set(cv2.CAP_PROP_AUTOFOCUS, 0)
                                cap.set(cv2.CAP_PROP_FOCUS, self.initial_focus_value)
                            except:
                                pass

                        return cap
                    else:
                        cap.release()

            print(f"❌ Failed to initialize {camera_name}")
            return None

        except Exception as e:
            print(f"❌ Error initializing {camera_name}: {e}")
            return None

    def _create_virtual_camera(self):
        """Tạo virtual camera để app không crash khi không có camera thật"""
        try:
            import numpy as np

            class VirtualCamera:
                def __init__(self):
                    self.opened = True
                    self.frame_count = 0

                def isOpened(self):
                    return self.opened

                def read(self):
                    # Tạo frame giả với text
                    frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    frame[:] = (50, 50, 50)  # Màu xám đậm

                    # Thêm text
                    import cv2
                    cv2.putText(frame, "Virtual Camera", (200, 200),
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                    cv2.putText(frame, f"Frame: {self.frame_count}", (200, 250),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                    cv2.putText(frame, "No physical camera detected", (150, 300),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

                    self.frame_count += 1
                    return True, frame

                def release(self):
                    self.opened = False

                def set(self, prop, value):
                    pass  # Ignore property settings

                def get(self, prop):
                    return 0  # Return default values

            print("🎭 Virtual camera created")
            return VirtualCamera()

        except Exception as e:
            print(f"❌ Error creating virtual camera: {e}")
            return None
    
    def generate_frames(self, camera_id):
        """Generate frames for video streaming"""
        frame_count = 0
        start_time = time.time()
        
        frame_count = 0
        while True:
            try:
                frame_count += 1

                # Check if streaming is active for this camera
                streaming_active = self.streaming_active_0 if camera_id == 0 else self.streaming_active_1
                capture_mode = self.capture_mode_0 if camera_id == 0 else self.capture_mode_1

                # DEBUG: Log stream state every 100 frames (reduced logging)
                if frame_count % 100 == 0:
                    print(f"🔍 Camera {camera_id} Frame {frame_count}: streaming_active={streaming_active}, capture_mode={capture_mode}")

                # If streaming is paused or in capture mode, STOP COMPLETELY
                if not streaming_active or capture_mode:
                    print(f"🛑 Camera {camera_id} STOPPED - No frames will be sent")
                    time.sleep(1.0)  # Just wait, don't send any frames
                    continue

                cap = self.camera_0 if camera_id == 0 else self.camera_1
                lock = self.lock_0 if camera_id == 0 else self.lock_1

                if cap is None or not cap.isOpened():
                    print(f"Camera {camera_id} not ready, waiting...")
                    time.sleep(2)
                    continue
                
                ret, frame = cap.read()
                if not ret or frame is None:
                    print(f"Camera {camera_id} read failed, reinitializing...")
                    self._reinitialize_camera(camera_id)
                    time.sleep(1)
                    continue
                
                # Store frame for capture
                with lock:
                    if camera_id == 0:
                        self.output_frame_0 = frame.copy()
                    else:
                        self.output_frame_1 = frame.copy()
                
                # FPS monitoring (every second) - DISABLED FOR DEBUG
                # frame_count += 1
                # elapsed = time.time() - start_time
                # if elapsed >= 1.0:
                #     fps = frame_count / elapsed
                #     print(f"[Camera {camera_id}] FPS: {fps:.1f}")
                #     frame_count = 0
                #     start_time = time.time()
                
                # Encode frame for streaming
                ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if not ret:
                    continue
                    
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                       
            except Exception as e:
                print(f"❌ Error in generate_frames for camera {camera_id}: {e}")
                time.sleep(1)

    def _reinitialize_camera(self, camera_id):
        """Reinitialize a specific camera using actual camera index"""
        try:
            if camera_id == 0:
                with self.lock_0:
                    if self.camera_0:
                        self.camera_0.release()
                    actual_index = getattr(self, 'business_card_camera_index', 0)
                    self.camera_0 = self._initialize_single_camera(actual_index, "Business Card")
                    print(f"🔄 Reinitialized Business Card camera using index {actual_index}")
            else:
                with self.lock_1:
                    if self.camera_1:
                        self.camera_1.release()
                    actual_index = getattr(self, 'face_camera_index', 1)
                    self.camera_1 = self._initialize_single_camera(actual_index, "Face")
                    print(f"🔄 Reinitialized Face camera using index {actual_index}")

        except Exception as e:
            print(f"❌ Error reinitializing camera {camera_id}: {e}")

    def video_feed(self, camera_id):
        """Video streaming route with WebRTC-only mode support"""
        if self.webrtc_only_mode:
            # Return placeholder image for WebRTC-only mode
            return Response(self.generate_placeholder_frames(camera_id),
                           mimetype='multipart/x-mixed-replace; boundary=frame')

        return Response(self.generate_frames(camera_id),
                        mimetype='multipart/x-mixed-replace; boundary=frame')

    def generate_placeholder_frames(self, camera_id):
        """Generate placeholder frames for WebRTC-only mode"""
        import numpy as np
        import cv2

        # Create placeholder image
        frame = np.zeros((480, 640, 3), dtype=np.uint8)

        # Add text overlay
        camera_name = "Business Card Camera" if camera_id == 0 else "Face Camera"
        text_lines = [
            "WebRTC Mode Active",
            f"{camera_name}",
            "Using Client Camera",
            "via Browser"
        ]

        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        color = (255, 255, 255)
        thickness = 2

        y_offset = 150
        for i, line in enumerate(text_lines):
            text_size = cv2.getTextSize(line, font, font_scale, thickness)[0]
            x = (640 - text_size[0]) // 2
            y = y_offset + i * 40
            cv2.putText(frame, line, (x, y), font, font_scale, color, thickness)

        # Add camera icon (simple rectangle)
        cv2.rectangle(frame, (270, 80), (370, 120), (100, 100, 100), 2)
        cv2.circle(frame, (320, 100), 15, (100, 100, 100), 2)

        while True:
            # Encode frame
            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            if ret:
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

            time.sleep(0.1)  # 10 FPS for placeholder



    def pause_stream(self, camera_id):
        """Pause camera stream for capture processing"""
        try:
            if camera_id == 0:
                self.streaming_active_0 = False
                self.capture_mode_0 = True
            else:
                self.streaming_active_1 = False
                self.capture_mode_1 = True

            print(f"📸 Camera {camera_id} stream paused for capture processing")
            return jsonify({
                'status': 'success',
                'message': f'Camera {camera_id} stream paused',
                'camera_id': camera_id
            })
        except Exception as e:
            print(f"❌ Error pausing camera {camera_id}: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def resume_stream(self, camera_id):
        """Resume camera stream after capture processing"""
        try:
            if camera_id == 0:
                self.streaming_active_0 = True
                self.capture_mode_0 = False
            else:
                self.streaming_active_1 = True
                self.capture_mode_1 = False

            print(f"▶️ Camera {camera_id} stream resumed")
            return jsonify({
                'status': 'success',
                'message': f'Camera {camera_id} stream resumed',
                'camera_id': camera_id
            })
        except Exception as e:
            print(f"❌ Error resuming camera {camera_id}: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def stop_all_streams(self):
        """Stop all camera streams"""
        try:
            self.streaming_active_0 = False
            self.streaming_active_1 = False
            self.capture_mode_0 = True
            self.capture_mode_1 = True

            print("⏹️ All camera streams stopped")
            return jsonify({
                'status': 'success',
                'message': 'All camera streams stopped'
            })
        except Exception as e:
            print(f"❌ Error stopping streams: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def test_camera_control(self, camera_id, action):
        """Test camera control for debugging"""
        try:
            print(f"🧪 TEST: Camera {camera_id} action: {action}")

            if action == 'stop':
                if camera_id == 0:
                    self.streaming_active_0 = False
                    self.capture_mode_0 = True
                    print(f"🛑 TEST: Camera 0 STOPPED - streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
                else:
                    self.streaming_active_1 = False
                    self.capture_mode_1 = True
                    print(f"🛑 TEST: Camera 1 STOPPED - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

                return jsonify({
                    'status': 'success',
                    'message': f'Camera {camera_id} stopped for testing',
                    'camera_id': camera_id,
                    'action': action
                })

            elif action == 'start':
                if camera_id == 0:
                    self.streaming_active_0 = True
                    self.capture_mode_0 = False
                    print(f"▶️ TEST: Camera 0 STARTED - streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
                else:
                    self.streaming_active_1 = True
                    self.capture_mode_1 = False
                    print(f"▶️ TEST: Camera 1 STARTED - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

                return jsonify({
                    'status': 'success',
                    'message': f'Camera {camera_id} started for testing',
                    'camera_id': camera_id,
                    'action': action
                })
            else:
                return jsonify({'status': 'error', 'message': 'Invalid action. Use stop or start'}), 400

        except Exception as e:
            print(f"❌ Test camera control error: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def test_simple(self):
        """Simple test route (REFACTORED)"""
        print("🧪🧪🧪 SIMPLE TEST ROUTE CALLED 🧪🧪🧪")
        return APIResponseFormatter.success_response(message='Simple test works!')

    def capture_step(self, camera_id):
        """Capture image from camera with IMMEDIATE stream stop"""
        print(f"🚨🚨🚨 CAPTURE_STEP CALLED FOR CAMERA {camera_id} 🚨🚨🚨")
        print(f"📸 Starting capture for camera {camera_id}")

        # STEP 1: IMMEDIATELY stop the camera stream
        print(f"🛑 STOPPING camera {camera_id} stream NOW...")
        if camera_id == 0:
            self.streaming_active_0 = False
            self.capture_mode_0 = True
            print(f"🛑 Camera 0: streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
        else:
            self.streaming_active_1 = False
            self.capture_mode_1 = True
            print(f"🛑 Camera 1: streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

        # STEP 2: Wait a moment to ensure stream stops
        import time
        time.sleep(0.2)  # Give time for stream to stop

        print(f"✅ Camera {camera_id} stream STOPPED for capture")

        # Ensure session exists (REFACTORED)
        SessionManager.ensure_session_exists(self.session_model, "camera capture")

        timestamp = path_manager.get_unix_timestamp()
        filename = None

        try:
            if camera_id == 0:
                with self.lock_0:
                    if self.output_frame_0 is not None:
                        filename = path_manager.get_captured_image_path('card', timestamp)
                        cv2.imwrite(filename, self.output_frame_0)
            elif camera_id == 1:
                with self.lock_1:
                    if self.output_frame_1 is not None:
                        filename = path_manager.get_captured_image_path('face', timestamp)
                        cv2.imwrite(filename, self.output_frame_1)
                        
            if filename:
                # Determine image type for session
                image_type = 'card' if camera_id == 0 else 'face'

                # Save to session
                session_image_path = self.session_model.save_captured_image(filename, image_type)

                print(f"✅ Captured {image_type} image: {filename}")
                print(f"📁 Session status: card={self.session_model.current_session.get('card_captured')}, face={self.session_model.current_session.get('face_captured')}")

                return jsonify({
                    'status': 'success',
                    'message': f'{image_type.title()} captured successfully',
                    'image_path': filename,
                    'session_path': session_image_path,
                    'session_id': self.session_model.current_session.get('session_id')
                })
            else:
                return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.'}), 500
                
        except Exception as e:
            print(f"Error capturing image: {str(e)}")
            return jsonify({'status': 'error', 'message': f'Lỗi khi chụp ảnh: {str(e)}'}), 500
            # Determine image type for session
            image_type = 'card' if camera_id == 0 else 'face'

            # Save to session
            session_image_path = self.session_model.save_captured_image(filename, image_type)

            print(f"✅ Captured {image_type} image: {filename}")
            print(f"📁 Session status: card={self.session_model.current_session.get('card_captured')}, face={self.session_model.current_session.get('face_captured')}")

            # STEP 3: Resume camera stream after successful capture
            print(f"▶️ Resuming camera {camera_id} stream after capture...")
            if camera_id == 0:
                self.streaming_active_0 = True
                self.capture_mode_0 = False
                print(f"▶️ Camera 0 resumed - streaming_active={self.streaming_active_0}, capture_mode={self.capture_mode_0}")
            else:
                self.streaming_active_1 = True
                self.capture_mode_1 = False
                print(f"▶️ Camera 1 resumed - streaming_active={self.streaming_active_1}, capture_mode={self.capture_mode_1}")

            return jsonify({
                'status': 'success',
                'message': f'{image_type.title()} captured successfully',
                'image_path': filename,
                'session_path': session_image_path,
                'session_id': self.session_model.current_session.get('session_id'),
                'stream_resumed': True
            })
        else:
            # Resume stream even on error
            print(f"❌ Capture failed, resuming camera {camera_id} stream...")
            if camera_id == 0:
                self.streaming_active_0 = True
                self.capture_mode_0 = False
            else:
                self.streaming_active_1 = True
                self.capture_mode_1 = False

            return jsonify({'status': 'error', 'message': 'Không thể chụp ảnh.', 'stream_resumed': True}), 500

    def upload_camera_image(self):
        """Handle image upload from client-side camera (WebRTC)"""

        try:
            if 'image' not in request.files:
                return jsonify({'status': 'error', 'message': 'No image file provided'}), 400

            file = request.files['image']
            camera_id = int(request.form.get('camera_id', 0))

            if file.filename == '':
                return jsonify({'status': 'error', 'message': 'No file selected'}), 400

            # Ensure session exists (REFACTORED)
            SessionManager.ensure_session_exists(self.session_model, "file upload")

            # Save uploaded file using PathManager
            timestamp = path_manager.get_unix_timestamp()
            image_type = 'card' if camera_id == 0 else 'face'
            filename = path_manager.get_captured_image_path(image_type, timestamp)

            file.save(filename)

            # Save to session
            session_image_path = self.session_model.save_captured_image(filename, image_type)

            print(f"✅ Client uploaded {image_type} image: {filename}")
            print(f"📁 Session status: card={self.session_model.current_session.get('card_captured')}, face={self.session_model.current_session.get('face_captured')}")

            return jsonify({
                'status': 'success',
                'message': f'{image_type.title()} uploaded successfully',
                'image_path': filename,
                'session_path': session_image_path,
                'session_id': self.session_model.current_session.get('session_id')
            })

        except Exception as e:
            print(f"❌ Error uploading image: {str(e)}")
            return jsonify({'status': 'error', 'message': f'Upload failed: {str(e)}'}), 500

    def adjust_focus(self):
        """Adjust focus for camera 0 (business card camera) - EXACT project_directory style"""
        data = request.get_json()
        new_focus_value = data.get('focus_value')

        if new_focus_value is None:
            return jsonify({'status': 'error', 'message': 'Thiếu giá trị focus.'}), 400

        try:
            new_focus_value = int(new_focus_value)
        except ValueError:
            return jsonify({'status': 'error', 'message': 'Giá trị focus không hợp lệ.'}), 400

        with self.lock_0:
            if self.camera_0 and self.camera_0.isOpened():
                self.camera_0.set(cv2.CAP_PROP_FOCUS, new_focus_value)
                self.initial_focus_value = new_focus_value
                print(f"Focus mới cho Logitech: {new_focus_value}")
                return APIResponseFormatter.success_response(message=f'Đã chỉnh nét thành {new_focus_value}.')
            else:
                return APIResponseFormatter.error_response('Camera Logitech không hoạt động.', status_code=500)

    def session_status(self):
        """Get current session status"""
        try:
            status = self.session_model.get_session_status()
            return jsonify({
                'status': 'success',
                **status
            })
        except Exception as e:
            print(f"❌ Session status error: {e}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    def reset_session(self):
        """Reset current session (REFACTORED)"""
        try:
            success = self.session_model.reset_session()
            if success:
                return APIResponseFormatter.success_response(message='Session reset successfully')
            else:
                return APIResponseFormatter.error_response('Failed to reset session', status_code=500)
        except Exception as e:
            return ErrorLogger.handle_api_error("Reset session", e)

    def camera_status_api(self):
        """API endpoint to get camera status with WebRTC mode support"""
        try:
            # Build camera status
            cameras = {}

            if self.webrtc_only_mode:
                # WebRTC-only mode - report cameras as available for client-side use
                cameras['0'] = {
                    'opened': True,
                    'streaming': True,
                    'resolution': "640x480",
                    'focus': self.initial_focus_value,
                    'mode': 'webrtc',
                    'source': 'client'
                }
                cameras['1'] = {
                    'opened': True,
                    'streaming': True,
                    'resolution': "640x480",
                    'focus': 'auto',
                    'mode': 'webrtc',
                    'source': 'client'
                }
                camera0_available = True
                camera1_available = True
            else:
                # Physical camera mode
                camera0_available = self.camera_0 and self.camera_0.isOpened()
                if camera0_available:
                    cameras['0'] = {
                        'opened': True,
                        'streaming': True,
                        'resolution': "640x480",
                        'focus': self.initial_focus_value,
                        'mode': 'physical',
                        'source': 'server'
                    }
                else:
                    cameras['0'] = {
                        'opened': False,
                        'streaming': False,
                        'resolution': 'N/A',
                        'focus': self.initial_focus_value,
                        'mode': 'physical',
                        'source': 'server'
                    }

                # Camera 1 status
                camera1_available = self.camera_1 and self.camera_1.isOpened()
                if camera1_available:
                    cameras['1'] = {
                        'opened': True,
                        'streaming': True,
                        'resolution': "640x480",
                        'focus': 'auto',
                        'mode': 'physical',
                        'source': 'server'
                    }
                else:
                    cameras['1'] = {
                        'opened': False,
                        'streaming': False,
                        'resolution': 'N/A',
                        'focus': 'auto',
                        'mode': 'physical',
                        'source': 'server'
                    }

            result = {
                'status': 'success',
                'cameras': cameras,
                'camera_count': 2 if (camera0_available and camera1_available) else (1 if (camera0_available or camera1_available) else 0),
                'camera0_available': camera0_available,  # Add explicit fields for JS
                'camera1_available': camera1_available,  # Add explicit fields for JS
                'webrtc_only_mode': self.webrtc_only_mode,  # Important for client-side logic
                'server_environment': self.is_server_environment,
                'config': {
                    'width': 640,
                    'height': 480,
                    'fps': 30,
                    'backend': 'WebRTC' if self.webrtc_only_mode else 'DirectShow'
                },
                # Thêm thông tin camera mapping
                'camera_mapping': {
                    'available_cameras': getattr(self, 'available_cameras', []),
                    'business_card_index': getattr(self, 'business_card_camera_index', 0),
                    'face_index': getattr(self, 'face_camera_index', 1),
                    'total_detected': len(getattr(self, 'available_cameras', []))
                }
            }

            print(f"📊 Camera status API response: camera0={camera0_available}, camera1={camera1_available}")
            return jsonify(result)

        except Exception as e:
            print(f"❌ Camera status error: {e}")
            return jsonify({
                'status': 'error',
                'message': str(e),
                'camera0_available': False,
                'camera1_available': False
            }), 500
    
    def get_camera_status(self):
        """Get current camera status"""
        try:
            # Check if cameras are initialized and working
            camera0_status = (self.camera_0 is not None and 
                             self.camera_0.isOpened() if hasattr(self, 'camera_0') else False)
            camera1_status = (self.camera_1 is not None and 
                             self.camera_1.isOpened() if hasattr(self, 'camera_1') else False)
            
            print(f"📊 Camera status check:")
            print(f"   Camera 0 (Business Card): {camera0_status}")
            print(f"   Camera 1 (Face): {camera1_status}")
            
            return jsonify({
                'camera0_available': camera0_status,
                'camera1_available': camera1_status,
                'camera0': camera0_status,  # Backward compatibility
                'camera1': camera1_status,  # Backward compatibility
                'status': 'success'
            })
            
        except Exception as e:
            print(f"❌ Error checking camera status: {e}")
            return jsonify({
                'camera0_available': False,
                'camera1_available': False,
                'camera0': False,
                'camera1': False,
                'status': 'error',
                'error': str(e)
            }), 500

    def get_camera_mapping(self):
        """API endpoint để lấy thông tin camera mapping - Get camera mapping information"""
        try:
            from flask import jsonify

            mapping_info = {
                'status': 'success',
                'available_cameras': getattr(self, 'available_cameras', []),
                'business_card_camera_index': getattr(self, 'business_card_camera_index', 0),
                'face_camera_index': getattr(self, 'face_camera_index', 1),
                'camera_0_actual_index': getattr(self, 'business_card_camera_index', 0),
                'camera_1_actual_index': getattr(self, 'face_camera_index', 1),
                'camera_0_status': self.camera_0 is not None and self.camera_0.isOpened() if hasattr(self, 'camera_0') else False,
                'camera_1_status': self.camera_1 is not None and self.camera_1.isOpened() if hasattr(self, 'camera_1') else False,
                'total_cameras': len(getattr(self, 'available_cameras', [])),
                'webrtc_only_mode': getattr(self, 'webrtc_only_mode', False)
            }

            print(f"📊 Camera mapping info: {mapping_info}")
            return jsonify(mapping_info)

        except Exception as e:
            print(f"❌ Error getting camera mapping: {e}")
            return jsonify({
                'status': 'error',
                'error': str(e),
                'available_cameras': [],
                'business_card_camera_index': 0,
                'face_camera_index': 1
            }), 500

    def cleanup(self):
        """Public cleanup method"""
        self.cleanup_cameras()

    def cleanup_cameras(self):
        """Cleanup camera resources (EXACT project_directory style)"""
        print("Cleaning up cameras...")

        # Release camera 0
        try:
            with self.lock_0:
                if self.camera_0:
                    self.camera_0.release()
                    print("Released camera 0")
                self.camera_0 = None
                self.output_frame_0 = None
        except Exception as e:
            print(f"Error releasing camera 0: {e}")

        # Release camera 1
        try:
            with self.lock_1:
                if self.camera_1:
                    self.camera_1.release()
                    print("Released camera 1")
                self.camera_1 = None
                self.output_frame_1 = None
        except Exception as e:
            print(f"Error releasing camera 1: {e}")

        # Clear OpenCV windows
        try:
            cv2.destroyAllWindows()
        except Exception as e:
            print(f"Error destroying OpenCV windows: {e}")

        print("Camera cleanup completed")


# Create global camera controller instance
camera_controller = CameraController()

def get_camera_blueprint():
    """Get camera controller blueprint"""
    return camera_controller.blueprint




