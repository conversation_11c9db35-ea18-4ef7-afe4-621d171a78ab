#!/usr/bin/env python3
"""
Duplicate Logic Refactor - Centralized utilities to eliminate code duplication
Tập trung các utility để loại bỏ duplicate logic
"""

from flask import jsonify
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import json
import time
import traceback
from PIL import Image


class SessionManager:
    """Centralized session management to eliminate duplicate session handling"""
    
    @staticmethod
    def ensure_session_exists(session_model, context: str = ""):
        """
        Đảm bảo session tồn tại - tạo mới nếu chưa có
        Ensure session exists - create new if not exists
        
        Args:
            session_model: Session model instance
            context: Context string for logging
            
        Returns:
            str: Session ID
        """
        if not session_model.current_session:
            session_id = session_model.create_session()
            print(f"🆕 Created new session{f' for {context}' if context else ''}: {session_id}")
            return session_id
        return session_model.current_session.get('session_id')


class APIResponseFormatter:
    """Centralized API response formatting to eliminate duplicate patterns"""
    
    @staticmethod
    def success_response(data: Any = None, message: str = None, **kwargs) -> tuple:
        """
        Tạo success response chuẩn
        Create standard success response
        
        Args:
            data: Response data
            message: Success message
            **kwargs: Additional fields
            
        Returns:
            tuple: (jsonify_response, status_code)
        """
        response = {
            'status': 'success',
            **kwargs
        }
        
        if message:
            response['message'] = message
            
        if data is not None:
            if isinstance(data, dict):
                response.update(data)
            else:
                response['data'] = data
                
        return jsonify(response), 200
    
    @staticmethod
    def error_response(message: str, status_code: int = 500, **kwargs) -> tuple:
        """
        Tạo error response chuẩn
        Create standard error response
        
        Args:
            message: Error message
            status_code: HTTP status code
            **kwargs: Additional fields
            
        Returns:
            tuple: (jsonify_response, status_code)
        """
        response = {
            'status': 'error',
            'message': message,
            **kwargs
        }
        
        return jsonify(response), status_code
    
    @staticmethod
    def session_response(session_data: Dict, message: str = None) -> tuple:
        """
        Tạo session response chuẩn
        Create standard session response
        
        Args:
            session_data: Session data
            message: Optional message
            
        Returns:
            tuple: (jsonify_response, status_code)
        """
        response = {
            'status': 'success',
            'session': session_data
        }
        
        if message:
            response['message'] = message
            
        return jsonify(response), 200


class RetryHandler:
    """Centralized retry logic for API calls and operations"""
    
    @staticmethod
    def retry_with_backoff(func, max_retries: int = 3, backoff_factor: float = 2.0, 
                          exceptions: tuple = (Exception,), context: str = ""):
        """
        Retry function with exponential backoff
        
        Args:
            func: Function to retry
            max_retries: Maximum number of retries
            backoff_factor: Backoff multiplier
            exceptions: Exceptions to catch and retry
            context: Context for logging
            
        Returns:
            Function result or raises last exception
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return func()
            except exceptions as e:
                last_exception = e
                
                if attempt < max_retries:
                    wait_time = backoff_factor ** attempt
                    print(f"🔄 {context} attempt {attempt + 1} failed: {e}")
                    print(f"   Retrying in {wait_time:.1f}s...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ {context} failed after {max_retries + 1} attempts")
                    
        raise last_exception
    
    @staticmethod
    def retry_with_backup_keys(func, key_manager, context: str = ""):
        """
        Retry function with backup API keys
        
        Args:
            func: Function to retry
            key_manager: Object with switch_to_backup_key() method
            context: Context for logging
            
        Returns:
            Function result or raises last exception
        """
        try:
            return func()
        except Exception as e:
            error_str = str(e).lower()
            
            # Check if it's a rate limit/quota error
            if any(keyword in error_str for keyword in ['rate limit', 'quota', '429']):
                print(f"🔄 {context} quota exceeded, trying backup key...")
                
                if key_manager.switch_to_backup_key():
                    try:
                        print(f"🔄 Retrying {context} with backup key...")
                        return func()
                    except Exception as retry_error:
                        print(f"❌ {context} backup key also failed: {retry_error}")
                else:
                    print(f"❌ All {context} API keys exhausted")
            
            raise e


class FileHandler:
    """Centralized file handling to eliminate duplicate file operations"""
    
    @staticmethod
    def safe_file_operation(operation: str, file_path: Union[str, Path], 
                          data: Any = None, encoding: str = 'utf-8') -> tuple:
        """
        Safe file operation with error handling
        
        Args:
            operation: 'read', 'write', 'json_read', 'json_write'
            file_path: Path to file
            data: Data to write (for write operations)
            encoding: File encoding
            
        Returns:
            tuple: (success: bool, result: Any, error: str)
        """
        try:
            file_path = Path(file_path)
            
            if operation == 'read':
                with open(file_path, 'r', encoding=encoding) as f:
                    return True, f.read(), None
                    
            elif operation == 'write':
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'w', encoding=encoding) as f:
                    f.write(data)
                return True, str(file_path), None
                
            elif operation == 'json_read':
                with open(file_path, 'r', encoding=encoding) as f:
                    return True, json.load(f), None
                    
            elif operation == 'json_write':
                file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'w', encoding=encoding) as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return True, str(file_path), None
                
            else:
                return False, None, f"Unknown operation: {operation}"
                
        except Exception as e:
            error_msg = f"File {operation} error for {file_path}: {e}"
            print(f"❌ {error_msg}")
            return False, None, error_msg


class ImageValidator:
    """Centralized image validation and processing"""
    
    @staticmethod
    def validate_image(image_path: Union[str, Path]) -> tuple:
        """
        Validate image file
        
        Args:
            image_path: Path to image file
            
        Returns:
            tuple: (is_valid: bool, error: str, image_info: dict)
        """
        try:
            with Image.open(image_path) as img:
                img.verify()
                
                # Get image info
                with Image.open(image_path) as img:  # Reopen after verify
                    image_info = {
                        'size': img.size,
                        'format': img.format,
                        'mode': img.mode
                    }
                
                return True, None, image_info
                
        except Exception as e:
            return False, str(e), {}
    
    @staticmethod
    def safe_image_operation(operation: str, image_path: Union[str, Path], 
                           **kwargs) -> tuple:
        """
        Safe image operation with error handling
        
        Args:
            operation: 'validate', 'resize', 'convert'
            image_path: Path to image
            **kwargs: Operation-specific parameters
            
        Returns:
            tuple: (success: bool, result: Any, error: str)
        """
        try:
            if operation == 'validate':
                return ImageValidator.validate_image(image_path)
                
            elif operation == 'resize':
                max_width = kwargs.get('max_width', 1024)
                max_height = kwargs.get('max_height', 1024)
                output_path = kwargs.get('output_path', image_path)
                
                with Image.open(image_path) as img:
                    img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
                    img.save(output_path, optimize=True, quality=85)
                
                return True, str(output_path), None
                
            else:
                return False, None, f"Unknown operation: {operation}"
                
        except Exception as e:
            error_msg = f"Image {operation} error: {e}"
            print(f"❌ {error_msg}")
            return False, None, error_msg


class ErrorLogger:
    """Centralized error logging and handling"""
    
    @staticmethod
    def log_error(context: str, error: Exception, include_traceback: bool = True):
        """
        Log error with context
        
        Args:
            context: Error context
            error: Exception object
            include_traceback: Whether to include full traceback
        """
        print(f"❌ {context} error: {error}")
        
        if include_traceback:
            traceback.print_exc()
    
    @staticmethod
    def handle_api_error(context: str, error: Exception) -> tuple:
        """
        Handle API error and return formatted response
        
        Args:
            context: Error context
            error: Exception object
            
        Returns:
            tuple: (jsonify_response, status_code)
        """
        ErrorLogger.log_error(context, error)
        
        return APIResponseFormatter.error_response(
            message=f"{context} failed: {str(error)}",
            status_code=500,
            context=context
        )


# Export all utilities
__all__ = [
    'SessionManager',
    'APIResponseFormatter', 
    'RetryHandler',
    'FileHandler',
    'ImageValidator',
    'ErrorLogger'
]
