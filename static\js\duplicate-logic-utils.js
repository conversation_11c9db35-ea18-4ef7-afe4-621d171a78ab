/**
 * Duplicate Logic Utils - Centralized JavaScript utilities
 * Tập trung các utility JavaScript để loại bỏ duplicate logic
 */

/**
 * Card Info Display Manager - Centralized card information display
 */
class CardInfoDisplayManager {
    constructor() {
        this.fieldMappings = {
            'name': ['cardName', 'full_name'],
            'title': ['cardTitle', 'position'],
            'company': ['cardCompany', 'organization'],
            'email': ['cardEmail'],
            'phone': ['cardPhone', 'mobile'],
            'address': ['cardAddress', 'location']
        };
    }

    /**
     * Update single field with fallback and loading states
     * @param {string} fieldId - Element ID
     * @param {any} value - Field value
     * @param {string} fallback - Fallback text
     * @param {boolean} isLoading - Loading state
     */
    updateField(fieldId, value, fallback = 'Không có dữ liệu', isLoading = false) {
        const element = document.getElementById(fieldId);
        if (!element) return;

        if (isLoading) {
            element.textContent = 'Đang tải...';
            element.style.color = 'var(--color-text-secondary, #666)';
            element.style.fontStyle = 'italic';
        } else {
            element.textContent = value || fallback;
            element.style.color = value ? 'var(--color-text-primary, #333)' : 'var(--color-text-secondary, #666)';
            element.style.fontStyle = 'normal';
        }
    }

    /**
     * Populate all card info fields
     * @param {Object} cardInfo - Card information object
     * @param {boolean} showLoading - Show loading state first
     */
    populateCardInfo(cardInfo, showLoading = false) {
        console.log('📄 Populating card info:', cardInfo);

        // Show loading state first if requested
        if (showLoading) {
            this.showLoadingState();
        }

        // Update each field with proper mapping
        Object.entries(this.fieldMappings).forEach(([key, elementIds]) => {
            const primaryElementId = elementIds[0];
            
            // Try to get value from different possible keys
            let value = null;
            for (const possibleKey of [key, ...elementIds.slice(1)]) {
                if (cardInfo && cardInfo[possibleKey]) {
                    value = cardInfo[possibleKey];
                    break;
                }
            }

            this.updateField(primaryElementId, value);
            console.log(`✅ Updated ${key}: ${value || 'N/A'}`);
        });

        console.log('✅ Card info populated successfully');
    }

    /**
     * Show loading state for all fields
     */
    showLoadingState() {
        console.log('⏳ Showing loading state for card info...');
        
        Object.values(this.fieldMappings).forEach(elementIds => {
            const primaryElementId = elementIds[0];
            this.updateField(primaryElementId, null, 'Đang tải...', true);
        });
    }

    /**
     * Clear all card info fields
     */
    clearCardInfo() {
        Object.values(this.fieldMappings).forEach(elementIds => {
            const primaryElementId = elementIds[0];
            this.updateField(primaryElementId, null, 'Chưa có dữ liệu');
        });
    }
}

/**
 * API Manager - Centralized API calls with retry logic
 */
class APIManager {
    constructor() {
        this.defaultRetries = 3;
        this.defaultDelay = 2000;
    }

    /**
     * Fetch with retry logic
     * @param {string} url - API endpoint
     * @param {Object} options - Fetch options
     * @param {number} maxRetries - Maximum retries
     * @param {number} delay - Delay between retries
     * @returns {Promise} - Fetch promise
     */
    async fetchWithRetry(url, options = {}, maxRetries = this.defaultRetries, delay = this.defaultDelay) {
        let lastError;

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                console.log(`📡 API call to ${url} (attempt ${attempt + 1}/${maxRetries + 1})`);
                
                const response = await fetch(url, options);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
                
            } catch (error) {
                lastError = error;
                console.error(`❌ API call failed (attempt ${attempt + 1}):`, error);
                
                if (attempt < maxRetries) {
                    console.log(`⏳ Retrying in ${delay / 1000}s...`);
                    await this.sleep(delay);
                    delay *= 1.5; // Exponential backoff
                }
            }
        }

        throw lastError;
    }

    /**
     * Get session status with retry
     * @returns {Promise} - Session data
     */
    async getSessionStatus() {
        return this.fetchWithRetry('/api/session_status');
    }

    /**
     * Get OCR results with retry
     * @returns {Promise} - OCR data
     */
    async getOCRResults() {
        return this.fetchWithRetry('/api/ocr_results');
    }

    /**
     * Get generated images with retry
     * @returns {Promise} - Images data
     */
    async getGeneratedImages() {
        return this.fetchWithRetry('/api/generated_images', {}, 5, 3000);
    }

    /**
     * Get camera status
     * @returns {Promise} - Camera status
     */
    async getCameraStatus() {
        return this.fetchWithRetry('/camera_status');
    }

    /**
     * Sleep utility
     * @param {number} ms - Milliseconds to sleep
     * @returns {Promise} - Sleep promise
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * Camera Status Manager - Centralized camera status handling
 */
class CameraStatusManager {
    constructor() {
        this.cameraStatus = [false, false]; // [camera0, camera1]
        this.statusElements = {
            card: null,
            face: null,
            status0: null,
            status1: null
        };
        
        this.initializeElements();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        this.statusElements.card = document.getElementById('cardStatus');
        this.statusElements.face = document.getElementById('faceStatus');
        this.statusElements.status0 = document.getElementById('status0');
        this.statusElements.status1 = document.getElementById('status1');
    }

    /**
     * Update camera status from API response
     * @param {Object} data - API response data
     */
    updateFromAPIResponse(data) {
        console.log('📊 Updating camera status from API:', data);

        // Handle different API response formats
        if (data.camera0_available !== undefined && data.camera1_available !== undefined) {
            // New API format with explicit fields
            this.cameraStatus[0] = data.camera0_available;
            this.cameraStatus[1] = data.camera1_available;
        } else if (data.cameras) {
            // Fallback to cameras object format
            this.cameraStatus[0] = data.cameras['0'] ? data.cameras['0'].opened : false;
            this.cameraStatus[1] = data.cameras['1'] ? data.cameras['1'].opened : false;
        } else {
            // Default to available if unclear
            console.warn('⚠️ Unclear camera status format, defaulting to available');
            this.cameraStatus[0] = true;
            this.cameraStatus[1] = true;
        }

        this.updateDisplay();
    }

    /**
     * Update camera status display
     */
    updateDisplay() {
        // Update Business Card camera (Camera 0) - LEFT SIDE
        if (this.statusElements.card) {
            if (this.cameraStatus[0]) {
                this.statusElements.card.textContent = '📄 Camera sẵn sàng - Đặt business card';
                this.statusElements.card.style.color = '#43e97b';
            } else {
                this.statusElements.card.textContent = '❌ Business Card camera không khả dụng';
                this.statusElements.card.style.color = '#ff6b6b';
            }
        }

        // Update Face camera (Camera 1) - RIGHT SIDE
        if (this.statusElements.face) {
            if (this.cameraStatus[1]) {
                this.statusElements.face.textContent = '👤 Camera sẵn sàng - Đặt khuôn mặt';
                this.statusElements.face.style.color = '#43e97b';
            } else {
                this.statusElements.face.textContent = '❌ Face camera không khả dụng';
                this.statusElements.face.style.color = '#ff6b6b';
            }
        }

        // Update status indicators
        if (this.statusElements.status0) {
            this.statusElements.status0.style.background = this.cameraStatus[0] ? '#43e97b' : '#ff6b6b';
        }

        if (this.statusElements.status1) {
            this.statusElements.status1.style.background = this.cameraStatus[1] ? '#43e97b' : '#ff6b6b';
        }

        console.log(`📊 Camera status updated: [${this.cameraStatus[0]}, ${this.cameraStatus[1]}]`);
    }

    /**
     * Check if both cameras are available
     * @returns {boolean} - True if both cameras available
     */
    areBothCamerasAvailable() {
        return this.cameraStatus[0] && this.cameraStatus[1];
    }

    /**
     * Get camera status array
     * @returns {Array} - Camera status array
     */
    getStatus() {
        return [...this.cameraStatus];
    }
}

/**
 * Message Manager - Centralized message display
 */
class MessageManager {
    /**
     * Show message with type
     * @param {string} message - Message text
     * @param {string} type - Message type (success, error, warning, info)
     * @param {number} duration - Display duration in ms
     */
    static showMessage(message, type = 'info', duration = 5000) {
        console.log(`📢 ${type.toUpperCase()}: ${message}`);

        // Try to use existing showMessage function if available
        if (typeof window.showMessage === 'function') {
            window.showMessage(message, type);
            return;
        }

        // Fallback: Create simple notification
        const notification = document.createElement('div');
        notification.className = `message message-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            background: ${this.getMessageColor(type)};
        `;

        document.body.appendChild(notification);

        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    }

    /**
     * Get message color by type
     * @param {string} type - Message type
     * @returns {string} - CSS color
     */
    static getMessageColor(type) {
        const colors = {
            success: '#43e97b',
            error: '#ff6b6b',
            warning: '#ffa726',
            info: '#42a5f5'
        };
        return colors[type] || colors.info;
    }
}

// Create global instances
window.cardInfoManager = new CardInfoDisplayManager();
window.apiManager = new APIManager();
window.cameraStatusManager = new CameraStatusManager();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CardInfoDisplayManager,
        APIManager,
        CameraStatusManager,
        MessageManager
    };
}

console.log('✅ Duplicate Logic Utils loaded successfully');
