"""
Processing Controller - Xử lý xử lý ảnh và tạo AI
Handle image processing and AI generation
"""

from flask import Blueprint, jsonify, request, redirect, url_for, render_template
import json
import traceback
from pathlib import Path
from datetime import datetime

from gemini_ocr_service import GeminiOCRService
from ai_generator import AIImageGenerator
from models.session_model import SessionModel
from ai_config import get_gemini_config
from utils.duplicate_logic_refactor import SessionManager, APIResponseFormatter, ErrorLogger  # Import refactor utilities


class ProcessingController:
    """Controller xử lý ảnh và tạo AI - Controller for image processing and AI generation"""

    def __init__(self):
        """Khởi tạo Processing Controller"""
        self.blueprint = Blueprint('processing', __name__)
        self.setup_routes()
        self.setup_models()

    def setup_routes(self):
        """Thiết lập các routes xử lý - Setup processing routes"""
        # Route xử lý ảnh chính
        self.blueprint.add_url_rule('/process_images', 'process_images', self.process_images, methods=['POST'])
        # Route retry AI generation
        self.blueprint.add_url_rule('/retry_ai_generation', 'retry_ai_generation', self.retry_ai_generation, methods=['POST'])

    def setup_models(self):
        """Khởi tạo các model xử lý và AI pipeline - Initialize processing models and AI pipeline"""
        self.ocr_service = GeminiOCRService()
        self.ai_generator = AIImageGenerator()
        self.session_model = SessionModel()

        # Khởi tạo two-stage AI pipeline
        try:
            from services.ai_pipeline_service import AIProcessingPipeline
            self.ai_pipeline = AIProcessingPipeline()
            print("✅ Two-Stage AI Pipeline initialized in Processing Controller")
        except Exception as e:
            print(f"⚠️ AI Pipeline initialization failed: {e}")
            self.ai_pipeline = None

    def process_images(self):
        """Xử lý ảnh đã chụp và tạo ảnh AI - Process captured images and generate AI image"""
        try:
            # Import camera controller để lấy session chung
            from controllers.camera_controller import camera_controller

            # Sử dụng session từ camera controller (instance chung) - REFACTORED
            if not camera_controller.session_model.current_session:
                return APIResponseFormatter.error_response(
                    'No active session found. Please capture images first.',
                    status_code=400
                )

            session = camera_controller.session_model.current_session
            print(f"📁 Using session: {session.get('session_id')}")
            print(f"📁 Session status: card={session.get('card_captured')}, face={session.get('face_captured')}")

            # Kiểm tra đã chụp đủ ảnh chưa
            if not session.get('card_captured') or not session.get('face_captured'):
                return jsonify({
                    'status': 'error',
                    'message': 'Please capture both business card and face images first.'
                }), 400

            # Lấy template prompt từ request
            prompt_template = 'cartoon'  # Mặc định
            try:
                if request.is_json and request.get_json():
                    data = request.get_json()
                    prompt_template = data.get('prompt_template', 'cartoon')
                    print(f"🎨 Selected prompt template: {prompt_template}")
                else:
                    print("🎨 Using default prompt template (no JSON data received)")
            except Exception as e:
                print(f"⚠️ Error parsing request data: {e}, using default prompt")

            # Sử dụng cấu hình AI với prompt đã chọn
            ai_config = get_gemini_config()
            ai_config['prompt_template'] = prompt_template
            print(f"🤖 Using AI config: Model={ai_config['model']}, Template={prompt_template}")

            # Cập nhật trạng thái session
            camera_controller.session_model.update_session(status='processing', ai_config=ai_config)

            # Lấy đường dẫn ảnh cần thiết
            card_image_path = session.get('card_image')
            face_image_path = session.get('face_image')

            # Xác thực đầu vào
            if not card_image_path or not Path(card_image_path).exists():
                return jsonify({
                    'status': 'error',
                    'message': 'Business card image not found'
                }), 500

            if not face_image_path or not Path(face_image_path).exists():
                return jsonify({
                    'status': 'error',
                    'message': 'Face image not found'
                }), 500

            # STEP 1: OCR Processing (Always execute first)
            print("📝 STEP 1: Starting OCR Processing...")
            try:
                ocr_result = self.ocr_service.extract_text_from_card(card_image_path)
                
                if ocr_result and isinstance(ocr_result, dict):
                    print("✅ OCR completed successfully!")
                    print(f"📄 Extracted: {ocr_result.get('name', 'N/A')} - {ocr_result.get('company', 'N/A')}")
                    
                    # Save OCR data to session immediately
                    camera_controller.session_model.save_card_info(ocr_result)
                    print("💾 OCR data saved to session")
                    
                else:
                    print("❌ OCR failed or returned invalid result")
                    return jsonify({
                        'status': 'error',
                        'message': 'OCR processing failed. Please try again.',
                        'step': 'ocr'
                    }), 500
                    
            except Exception as e:
                print(f"❌ OCR Error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': f'OCR processing error: {str(e)}',
                    'step': 'ocr'
                }), 500

            # STEP 2: AI Image Generation (Optional, can fail gracefully)
            print("🎨 STEP 2: Starting AI Image Generation...")
            generation_result = None
            generation_error = None
            
            try:
                if self.ai_generator:
                    # Try AI generation
                    generation_result = self.ai_generator.generate_ai_image(
                        face_image_path=face_image_path,
                        card_info=ocr_result,
                        prompt_template=prompt_template,
                        session_id=session.get('session_id')
                    )
                    
                    if generation_result and generation_result.get('success'):
                        print("✅ AI Image Generation completed successfully!")
                        print(f"🖼️ Generated {len(generation_result.get('generated_images', []))} images")

                        # Save generated images to session
                        generated_images = generation_result.get('generated_images', [])
                        camera_controller.session_model.save_generated_images(generated_images)
                        print("💾 Generated images saved to session")
                    else:
                        generation_error = generation_result.get('error', 'AI generation failed') if generation_result else 'AI generator returned no result'
                        print(f"⚠️ AI Generation failed: {generation_error}")
                        
                else:
                    generation_error = "AI generator not available"
                    print("⚠️ AI generator not initialized")
                    
            except Exception as e:
                generation_error = str(e)
                print(f"❌ AI Generation Error: {e}")

            # Update session status based on results
            if generation_result and generation_result.get('success'):
                # Both OCR and AI generation successful
                camera_controller.session_model.update_session(status='completed')
                print("✅ Session completed successfully (OCR + AI)")
                
                return jsonify({
                    'status': 'success',
                    'message': 'Processing completed successfully',
                    'card_info': ocr_result,
                    'generated_images': generation_result.get('generated_images', []),
                    'session_id': session.get('session_id'),
                    'processing_steps': {
                        'ocr': 'success',
                        'ai_generation': 'success'
                    }
                })
            else:
                # OCR successful, AI generation failed
                camera_controller.session_model.update_session(
                    status='ocr_completed',
                    ai_generation_error=generation_error
                )
                print("⚠️ Session completed with OCR only (AI generation failed)")
                
                return jsonify({
                    'status': 'partial_success',
                    'message': 'OCR completed successfully. AI generation failed but can be retried.',
                    'card_info': ocr_result,
                    'generated_images': [],
                    'session_id': session.get('session_id'),
                    'processing_steps': {
                        'ocr': 'success',
                        'ai_generation': 'failed'
                    },
                    'ai_generation_error': generation_error,
                    'can_retry_ai': True
                })

        except Exception as e:
            print(f"❌ Processing error: {e}")
            traceback.print_exc()
            return jsonify({
                'status': 'error',
                'message': f'Processing failed: {str(e)}'
            }), 500

    def retry_ai_generation(self):
        """Retry AI generation for existing session with OCR data"""
        try:
            # Import camera controller để lấy session chung
            from controllers.camera_controller import camera_controller

            # Check if session exists - REFACTORED
            if not camera_controller.session_model.current_session:
                return APIResponseFormatter.error_response(
                    'No active session found. Please capture images first.',
                    status_code=400
                )

            session = camera_controller.session_model.current_session
            print(f"🔄 Retrying AI generation for session: {session.get('session_id')}")

            # Check if OCR data exists
            if not session.get('card_info'):
                return jsonify({
                    'status': 'error',
                    'message': 'No OCR data found. Please run OCR processing first.'
                }), 400

            # Get face image path
            face_image_path = session.get('face_image')
            if not face_image_path or not Path(face_image_path).exists():
                return jsonify({
                    'status': 'error',
                    'message': 'Face image not found. Please capture face image first.'
                }), 400

            # Get prompt template from request
            prompt_template = 'cartoon'  # Default
            try:
                if request.is_json and request.get_json():
                    data = request.get_json()
                    prompt_template = data.get('prompt_template', 'cartoon')
                    print(f"🎨 Using prompt template: {prompt_template}")
            except Exception as e:
                print(f"⚠️ Error parsing request data: {e}, using default prompt")

            # Get OCR data from session
            card_info = session.get('card_info')
            print(f"📄 Using existing OCR data: {card_info.get('name', 'N/A')} - {card_info.get('company', 'N/A')}")

            # Try AI generation
            print("🎨 Starting AI Image Generation retry...")
            try:
                if self.ai_generator:
                    generation_result = self.ai_generator.generate_ai_image(
                        face_image_path=face_image_path,
                        card_info=card_info,
                        prompt_template=prompt_template,
                        session_id=session.get('session_id')
                    )

                    if generation_result and generation_result.get('success'):
                        print("✅ AI Image Generation retry successful!")
                        print(f"🖼️ Generated {len(generation_result.get('generated_images', []))} images")

                        # Update session to completed
                        camera_controller.session_model.update_session(status='completed')

                        return jsonify({
                            'status': 'success',
                            'message': 'AI generation completed successfully',
                            'card_info': card_info,
                            'generated_images': generation_result.get('generated_images', []),
                            'session_id': session.get('session_id'),
                            'retry': True
                        })
                    else:
                        generation_error = generation_result.get('error', 'AI generation failed') if generation_result else 'AI generator returned no result'
                        print(f"❌ AI Generation retry failed: {generation_error}")

                        return jsonify({
                            'status': 'error',
                            'message': f'AI generation retry failed: {generation_error}',
                            'card_info': card_info,
                            'generated_images': [],
                            'session_id': session.get('session_id'),
                            'retry': True,
                            'error_details': generation_error
                        }), 500
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'AI generator not available',
                        'card_info': card_info,
                        'retry': True
                    }), 500

            except Exception as e:
                print(f"❌ AI Generation retry error: {e}")
                return jsonify({
                    'status': 'error',
                    'message': f'AI generation retry failed: {str(e)}',
                    'card_info': card_info,
                    'retry': True
                }), 500

        except Exception as e:
            print(f"❌ Retry AI generation error: {e}")
            traceback.print_exc()
            return jsonify({
                'status': 'error',
                'message': f'Retry failed: {str(e)}'
            }), 500


# Create global processing controller instance
processing_controller = ProcessingController()

def get_processing_blueprint():
    """Get processing controller blueprint"""
    return processing_controller.blueprint
