#!/usr/bin/env python3
"""
Gemini OCR Service - CHỈ DÙNG GEMINI 2.5 CHO OCR
Dịch vụ OCR sử dụng Gemini 2.5 để đọc thông tin từ name card
"""

import os  # Thư viện hệ điều hành
import json  # Th<PERSON> viện xử lý JSON
import base64  # Thư viện mã hóa base64 cho ảnh
import requests  # Thư viện gửi HTTP requests
from pathlib import Path  # Thư viện xử lý đường dẫn file

class GeminiOCRService:
    """
    Dịch vụ OCR Gemini - Giai đoạn 1 của Two-Stage AI Pipeline
    Gemini OCR Service - Stage 1 of Two-Stage AI Pipeline

    Sử dụng Gemini 2.5 Flash chuyên biệt cho Optical Character Recognition (OCR)
    Uses Gemini 2.5 Flash specifically for Optical Character Recognition (OCR)
    với độ chính xác cao thông qua:
    with enhanced accuracy through:
    - Tiền xử lý ảnh (resize, tăng cường độ tương phản/độ sắc nét, unsharp mask)
    - Image preprocessing (resize, enhance contrast/sharpness, unsharp mask)
    - Thiết lập API xác định (temperature=0.0, topK=1, topP=0.1)
    - Deterministic API settings (temperature=0.0, topK=1, topP=0.1)
    - Prompt OCR nâng cao để tối đa hóa độ chính xác nhận dạng văn bản
    - Enhanced OCR prompts for maximum text recognition accuracy
    """

    def __init__(self):
        """Khởi tạo dịch vụ OCR"""
        self.load_api_key()  # Tải API key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"  # URL cơ sở của API
        self.model_name = "gemini-2.5-flash"  # Model chuyên biệt cho độ chính xác OCR - Specifically for OCR accuracy
        print(f"👁️ Gemini 2.5 Flash OCR Service initialized")
        print(f"   Model: {self.model_name}")
        print(f"   Purpose: Stage 1 - OCR Processing")
        print(f"   Features: Image preprocessing, deterministic settings, enhanced prompts")

    def load_api_key(self):
        """Tải Gemini API key với hỗ trợ rotation - Load Gemini API key with rotation support"""

        # Thử tải từ file .env - Try to load from .env file
        env_file = Path(".env")
        if env_file.exists():  # Nếu file .env tồn tại
            with open(env_file, 'r') as f:  # Mở file để đọc
                for line in f:  # Đọc từng dòng
                    if '=' in line and not line.startswith('#'):  # Nếu dòng chứa biến môi trường
                        key, value = line.strip().split('=', 1)  # Tách key và value
                        os.environ[key] = value  # Đặt biến môi trường

        # Lấy tất cả API key có sẵn từ environment variables - Get all available API keys
        api_keys = [
            os.getenv('GEMINI_API_KEY'),  # API key chính từ .env
            os.getenv('GEMINI_API_KEY_2'),  # API key dự phòng 1 từ .env
            os.getenv('GEMINI_API_KEY_3')  # API key dự phòng 2 từ .env
        ]

        # Lọc bỏ key None/rỗng và key placeholder - Filter out None/empty keys and placeholder keys
        valid_keys = [key for key in api_keys if key and key.strip() and not key.startswith('YOUR_')]  # Chỉ lấy key hợp lệ

        if not valid_keys:  # Nếu không có key hợp lệ nào
            self.gemini_key = ''  # Đặt key chính rỗng
            self.backup_keys = []  # Danh sách key dự phòng rỗng
            print("⚠️ Không tìm thấy Gemini API key hợp lệ nào")  # Log cảnh báo không có key
            return  # Thoát khỏi function

        # Sử dụng key hợp lệ đầu tiên làm chính - Use first valid key as primary
        self.gemini_key = valid_keys[0]  # Gán key đầu tiên làm key chính
        self.backup_keys = valid_keys[1:] if len(valid_keys) > 1 else []  # Các key còn lại làm dự phòng

        print(f"✅ Gemini API key loaded for OCR")  # Log tải key thành công
        print(f"   API keys available: {len(valid_keys)}")  # Log số lượng key có sẵn
        if self.backup_keys:  # Nếu có key dự phòng
            print(f"   Backup keys available: {len(self.backup_keys)}")  # Log số lượng key dự phòng

    def switch_to_backup_key(self):
        """Chuyển sang API key dự phòng khi bị giới hạn tốc độ - Switch to backup API key when rate limit hit"""
        if not self.backup_keys:  # Nếu không có key dự phòng nào
            print("   ❌ Không còn API key dự phòng nào khả dụng cho OCR")  # Log không có key dự phòng
            return False  # Trả về False để báo thất bại

        # Chuyển key hiện tại vào cuối danh sách dự phòng - Move current key to end of backup list
        self.backup_keys.append(self.gemini_key)  # Thêm key hiện tại vào cuối danh sách dự phòng

        # Sử dụng key dự phòng đầu tiên làm key chính mới - Use first backup key as new primary
        self.gemini_key = self.backup_keys.pop(0)  # Lấy key đầu tiên từ danh sách dự phòng

        print(f"   🔄 OCR đã chuyển sang API key dự phòng: {self.gemini_key[:20]}...")  # Log chuyển sang key dự phòng
        print(f"   📊 Còn lại {len(self.backup_keys)} key dự phòng cho OCR")  # Log số key dự phòng còn lại

        return True  # Trả về True để báo chuyển key thành công

    def extract_text_from_card(self, card_path):
        """Trích xuất văn bản từ name card sử dụng Gemini 2.5 - Extract text from business card using Gemini 2.5"""

        print(f"📄 Extracting text from: {Path(card_path).name}")  # Log file đang xử lý

        if not self.gemini_key:  # Nếu không có API key
            return self._get_fallback_card_info()  # Trả về thông tin mặc định

        try:
            # Mã hóa ảnh name card - Encode card image
            card_data = self._encode_image(card_path)  # Mã hóa ảnh thành base64
            if not card_data:  # Nếu không mã hóa được
                return self._get_fallback_card_info()  # Trả về thông tin mặc định

            headers = {"Content-Type": "application/json"}  # Header cho HTTP request
            
            # Prompt OCR chi tiết để trích xuất thông tin name card - Detailed OCR prompt for business card extraction
            ocr_prompt = """Bạn là Gemini 2.5 Flash, hệ thống OCR tiên tiến nhất. Phân tích ảnh name card này với ĐỘ CHÍNH XÁC TỐI ĐA.

🎯 NHIỆM VỤ: Trích xuất TẤT CẢ thông tin văn bản hiển thị từ name card này với độ chính xác 100%.

🔬 PHƯƠNG PHÁP PHÂN TÍCH:
1. QUÉT toàn bộ ảnh một cách có hệ thống - từng pixel, từng ký tự
2. NHẬN DIỆN tất cả phần tử văn bản theo thứ tự đọc (trên xuống dưới, trái sang phải)
3. ĐỌC từng ký tự với sự cẩn thận tối đa để tránh lỗi OCR:
   • Số 0 vs Chữ O
   • Số 1 vs Chữ l vs Chữ I
   • Số 5 vs Chữ S
   • Số 6 vs Chữ G
   • Số 8 vs Chữ B
   • Số 9 vs Chữ g
4. BẢO TOÀN chính xác chính tả, viết hoa, dấu câu, khoảng cách
5. XỬ LÝ dấu tiếng Việt hoàn hảo: á à ả ã ạ ă ắ ằ ẳ ẵ ặ â ấ ầ ẩ ẫ ậ đ é è ẻ ẽ ẹ ê ế ề ể ễ ệ í ì ỉ ĩ ị ó ò ỏ õ ọ ô ố ồ ổ ỗ ộ ơ ớ ờ ở ỡ ợ ú ù ủ ũ ụ ư ứ ừ ử ữ ự ý ỳ ỷ ỹ ỵ

📋 THÔNG TIN CẦN TRÍCH XUẤT:
• TÊN: Họ tên đầy đủ của người (có thể bao gồm danh xưng: Ông, Bà, Tiến sĩ, CEO, Giám đốc, v.v.)
• CHỨC VỤ: Vị trí công việc/vai trò (Quản lý, Kỹ sư, Lập trình viên, Phân tích viên, v.v.)
• CÔNG TY: Tên tổ chức với viết hoa chính xác
• EMAIL: Địa chỉ email đầy đủ với tên miền
• ĐIỆN THOẠI: Số điện thoại với mã quốc gia, định dạng
• WEBSITE: URL website đầy đủ
• ĐỊA CHỈ: Địa chỉ đầy đủ với đường, quận, thành phố

🎯 QUY TẮC TRÍCH XUẤT:
• Trích xuất văn bản CHÍNH XÁC như xuất hiện trên thẻ
• Duy trì định dạng và viết hoa gốc
• Nếu văn bản không rõ hoặc hiển thị một phần, trích xuất những gì có thể thấy rõ
• KHÔNG đoán hoặc thêm thông tin không nhìn thấy
• KHÔNG sửa "lỗi" rõ ràng - trích xuất chính xác những gì được hiển thị
• Chú ý đặc biệt đến các ký tự trông giống nhau

📤 ĐỊNH DẠNG ĐẦU RA YÊU CẦU:
Chỉ trả về cấu trúc JSON này KHÔNG có văn bản bổ sung:

{
    "name": "Họ tên đầy đủ chính xác như được viết",
    "title": "Chức danh công việc chính xác như được viết",
    "company": "Tên công ty chính xác như được viết",
    "email": "<EMAIL>",
    "phone": "Số điện thoại với định dạng",
    "website": "URL website như được hiển thị",
    "address": "Địa chỉ đầy đủ như được viết"
}

⚠️ YÊU CẦU QUAN TRỌNG - CRITICAL REQUIREMENTS:
• Sử dụng chuỗi rỗng "" cho các trường không nhìn thấy - Use empty string "" for fields not visible
• KHÔNG có văn bản bên ngoài đối tượng JSON - NO text outside the JSON object
• KHÔNG có giải thích hoặc comment - NO explanations or comments
• Độ chính xác hoàn hảo ở cấp độ ký tự - PERFECT character-level accuracy
• Mong đợi độ chính xác Gemini 2.5 Flash - Gemini 2.5 Flash precision expected"""

            # Tạo payload cho API request - Create payload for API request
            payload = {
                "contents": [{  # Nội dung request
                    "parts": [  # Các phần của request
                        {"text": ocr_prompt},  # Phần prompt OCR
                        {
                            "inline_data": {  # Dữ liệu ảnh inline
                                "mime_type": "image/jpeg",  # Loại MIME
                                "data": card_data  # Dữ liệu ảnh base64
                            }
                        }
                    ]
                }],
                "generationConfig": {  # Cấu hình tạo response
                    "temperature": 0.0,  # Nhiệt độ tối thiểu để có độ chính xác tối đa - Minimum temperature for maximum accuracy
                    "topK": 1,           # Đầu ra xác định nhất - Most deterministic output
                    "topP": 0.1,         # Lấy mẫu rất tập trung - Very focused sampling
                    "maxOutputTokens": 2048,  # Số token tối đa
                    "candidateCount": 1  # Số candidate
                }
            }

            # Gửi POST request đến Gemini API - Send POST request to Gemini API
            response = requests.post(
                f"{self.base_url}/models/{self.model_name}:generateContent?key={self.gemini_key}",  # URL API
                headers=headers,  # Header request
                json=payload,  # Dữ liệu JSON
                timeout=120  # Timeout 120 giây
            )

            if response.status_code == 200:  # Nếu request thành công
                result = response.json()  # Parse JSON response

                if 'candidates' in result and result['candidates']:  # Nếu có candidates
                    try:
                        # Lấy text từ candidate đầu tiên - Get text from first candidate
                        ocr_text = result['candidates'][0]['content']['parts'][0].get('text', '')

                        print("✅ Gemini 2.5 OCR successful!")  # Log OCR thành công

                        # Debug: Hiển thị response OCR thô - Debug: Show raw OCR response
                        print(f"📄 Raw OCR Response (first 500 chars):")  # Log response thô
                        print(f"   {ocr_text[:500]}...")  # In 500 ký tự đầu

                        # Debug: Hiển thị toàn bộ response nếu ngắn - Debug: Show full response if short
                        if len(ocr_text) <= 1000:
                            print(f"📄 Full OCR Response ({len(ocr_text)} chars):")
                            print(f"   '{ocr_text}'")

                        # Debug: Kiểm tra response có rỗng không - Debug: Check if response is empty
                        if not ocr_text or ocr_text.strip() == "":
                            print("⚠️ OCR response is empty or whitespace only!")
                            return self._get_fallback_card_info()  # Trả về thông tin mặc định

                        # Parse kết quả JSON - Parse JSON result
                        card_info = self._parse_json_result(ocr_text)  # Gọi hàm parse JSON

                        # Đầu ra debug nâng cao với validation - Enhanced debug output with validation
                        print(f"📊 Extracted Information:")  # Log thông tin đã trích xuất
                        print(f"   👤 Name: '{card_info.get('name', 'N/A')}'")  # Log tên
                        print(f"   💼 Title: '{card_info.get('title', 'N/A')}'")  # Log chức vụ
                        print(f"   🏢 Company: '{card_info.get('company', 'N/A')}'")  # Log công ty
                        print(f"   📧 Email: '{card_info.get('email', 'N/A')}'")  # Log email
                        print(f"   📱 Phone: '{card_info.get('phone', 'N/A')}'")  # Log điện thoại
                        print(f"   🌐 Website: '{card_info.get('website', 'N/A')}'")  # Log website
                        print(f"   📍 Address: '{card_info.get('address', 'N/A')}'")  # Log địa chỉ

                        # Validate chất lượng trích xuất - Validate extraction quality
                        fields_with_data = sum(1 for v in card_info.values() if v and v.strip() and v != 'N/A')  # Đếm field có dữ liệu
                        print(f"📈 Extraction Quality: {fields_with_data}/7 fields extracted")  # Log chất lượng

                        if fields_with_data < 3:  # Nếu ít hơn 3 field có dữ liệu
                            print("⚠️ Low extraction quality - consider image quality or OCR prompt improvements")  # Log cảnh báo chất lượng thấp

                        return card_info  # Trả về thông tin card
                    except (KeyError, IndexError) as parse_error:
                        print(f"⚠️ Error parsing OCR response: {parse_error}")  # Log lỗi parse response
                        return self._get_fallback_card_info()  # Trả về thông tin mặc định
                else:
                    print("⚠️ No candidates in OCR response")  # Log không có candidates
                    return self._get_fallback_card_info()  # Trả về thông tin mặc định
            else:
                print(f"❌ Gemini OCR error: HTTP {response.status_code}")  # Log lỗi HTTP

                # Xử lý lỗi 429 (Rate Limit) cho OCR - Handle 429 (Rate Limit) for OCR
                if response.status_code == 429:
                    print("   🔄 OCR gặp lỗi giới hạn quota, đang thử API key dự phòng...")
                    if self.switch_to_backup_key():
                        print("   🔄 Đang thử lại OCR với API key dự phòng...")
                        try:
                            # Thử lại với key dự phòng - Retry with backup key
                            return self.extract_text_from_card(card_path)
                        except Exception as retry_error:
                            print(f"   ❌ OCR với API key dự phòng cũng thất bại: {retry_error}")
                    else:
                        print("   ❌ Tất cả OCR API keys đều hết quota")
                        print("   💡 Đề xuất: Chờ reset quota (24h) hoặc kiểm tra billing")

                if response.text:  # Nếu có response text
                    print(f"   Response: {response.text[:200]}...")  # Log 200 ký tự đầu của response
                return self._get_fallback_card_info()  # Trả về thông tin mặc định

        except Exception as e:
            print(f"❌ Gemini OCR exception: {e}")  # Log exception OCR

            # Kiểm tra xem có phải lỗi rate limit không - Check if it's a rate limit error
            error_str = str(e).lower()
            if 'rate limit' in error_str or 'quota' in error_str or '429' in error_str:
                print("   🔄 OCR exception chứa lỗi quota, đang thử API key dự phòng...")
                if self.switch_to_backup_key():
                    print("   🔄 Đang thử lại OCR với API key dự phòng...")
                    try:
                        # Thử lại với key dự phòng - Retry with backup key
                        return self.extract_text_from_card(card_path)
                    except Exception as retry_error:
                        print(f"   ❌ OCR retry với API key dự phòng cũng thất bại: {retry_error}")
                else:
                    print("   ❌ Tất cả OCR API keys đều hết quota")

            return self._get_fallback_card_info()  # Trả về thông tin mặc định

    def _parse_json_result(self, ocr_text):
        """Parse kết quả JSON từ Gemini OCR với fallback thông minh - Parse JSON result from Gemini OCR with smart fallback"""

        try:
            # Thử parse JSON trước - Try JSON parsing first
            start = ocr_text.find('{')  # Tìm vị trí bắt đầu JSON
            end = ocr_text.rfind('}') + 1  # Tìm vị trí kết thúc JSON

            if start >= 0 and end > start:  # Nếu tìm thấy JSON hợp lệ
                json_str = ocr_text[start:end]  # Lấy chuỗi JSON

                try:
                    import json  # Import thư viện JSON
                    parsed = json.loads(json_str)  # Parse chuỗi JSON

                    # Làm sạch và validate dữ liệu đã parse - Clean and validate the parsed data
                    card_info = {
                        'name': str(parsed.get('name', '')).strip(),  # Tên - làm sạch khoảng trắng
                        'title': str(parsed.get('title', '')).strip(),  # Chức vụ - làm sạch khoảng trắng
                        'company': str(parsed.get('company', '')).strip(),  # Công ty - làm sạch khoảng trắng
                        'email': str(parsed.get('email', '')).strip(),  # Email - làm sạch khoảng trắng
                        'phone': str(parsed.get('phone', '')).strip(),  # Điện thoại - làm sạch khoảng trắng
                        'website': str(parsed.get('website', '')).strip(),  # Website - làm sạch khoảng trắng
                        'address': str(parsed.get('address', '')).strip()  # Địa chỉ - làm sạch khoảng trắng
                    }

                    # Kiểm tra chất lượng OCR - Check OCR quality
                    non_empty_fields = sum(1 for v in card_info.values() if v and str(v).strip())
                    total_fields = len(card_info)

                    print(f"📊 OCR Quality Check: {non_empty_fields}/{total_fields} fields extracted")

                    # Nếu không có field nào được extract, báo lỗi thay vì fake data
                    if non_empty_fields == 0:
                        print("❌ OCR failed completely - no fields extracted")
                        print("🔄 Trying to re-process with enhanced settings...")
                        # Có thể thử lại với settings khác hoặc trả về lỗi
                        return None  # Trả về None thay vì fake data

                    # Chỉ giữ lại data thật, không điền fake data
                    # Only keep real data, don't fill fake data

                    if card_info is None:
                        print("❌ OCR extraction completely failed")
                        return self._get_fallback_card_info()  # Trả về fallback

                    print("✅ JSON parsing successful")  # Log parse JSON thành công
                    self._print_card_info(card_info)  # In thông tin card
                    return card_info  # Trả về thông tin card

                except json.JSONDecodeError:
                    print("⚠️ JSON decode failed, using smart text parsing")  # Log JSON decode thất bại
                    return self._smart_text_parsing(ocr_text)  # Chuyển sang parse text thông minh
            else:
                print("⚠️ No JSON found, using smart text parsing")  # Log không tìm thấy JSON
                return self._smart_text_parsing(ocr_text)  # Chuyển sang parse text thông minh

        except Exception as e:
            print(f"⚠️ Parsing error: {e}")  # Log lỗi parsing
            return self._smart_text_parsing(ocr_text)  # Chuyển sang parse text thông minh

    def _smart_text_parsing(self, ocr_text):
        """Parse text thông minh khi JSON thất bại - Smart text parsing when JSON fails"""

        try:
            print("🔍 Using smart text parsing...")  # Log sử dụng parse text thông minh

            # Khởi tạo với các field rỗng - KHÔNG CÓ GIÁ TRỊ MẶC ĐỊNH! - Initialize with empty fields - NO DEFAULTS!
            card_info = {
                'name': '',  # Tên rỗng
                'title': '',  # Chức vụ rỗng
                'company': '',  # Công ty rỗng
                'email': '',  # Email rỗng
                'phone': '',  # Điện thoại rỗng
                'website': '',  # Website rỗng
                'address': ''  # Địa chỉ rỗng
            }

            # Trích xuất thông tin sử dụng pattern nâng cao - Extract information using advanced patterns
            import re  # Import thư viện regex

            # Debug: Kiểm tra OCR text trước khi split - Debug: Check OCR text before splitting
            print(f"📄 OCR text length: {len(ocr_text)} characters")
            if len(ocr_text) <= 200:
                print(f"📄 OCR text content: '{ocr_text}'")

            lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]  # Tách thành các dòng và loại bỏ dòng trống

            print(f"📄 Analyzing {len(lines)} lines of extracted text...")  # Log số dòng text cần phân tích

            # Debug: Nếu không có dòng nào, thử split bằng cách khác - Debug: If no lines, try different splitting
            if len(lines) == 0:
                print("⚠️ No lines found after splitting by \\n, trying alternative methods...")
                # Thử split bằng space hoặc comma
                words = [word.strip() for word in ocr_text.split() if word.strip()]
                if len(words) > 0:
                    print(f"📄 Found {len(words)} words when splitting by space")
                    lines = [' '.join(words[i:i+3]) for i in range(0, len(words), 3)]  # Nhóm 3 từ thành 1 dòng
                    print(f"📄 Created {len(lines)} artificial lines from words")
                else:
                    print("⚠️ No words found either, OCR may have failed completely")
                    return self._get_fallback_card_info()

            for i, line in enumerate(lines):  # Lặp qua từng dòng
                line_lower = line.lower()  # Chuyển thành chữ thường để so sánh
                print(f"   Line {i+1}: '{line}'")  # Log từng dòng

                # Trích xuất email với nhiều pattern - Email extraction with multiple patterns
                if not card_info['email']:  # Nếu chưa tìm thấy email
                    email_patterns = [  # Danh sách pattern email
                        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Pattern email chuẩn
                        r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}',  # Pattern email đơn giản
                    ]

                    for pattern in email_patterns:  # Lặp qua từng pattern
                        emails = re.findall(pattern, line, re.IGNORECASE)  # Tìm email trong dòng
                        if emails:  # Nếu tìm thấy email
                            card_info['email'] = emails[0]  # Lấy email đầu tiên
                            print(f"      ✅ Found email: {emails[0]}")  # Log email tìm thấy
                            break  # Thoát khỏi vòng lặp

                # Trích xuất số điện thoại với pattern Việt Nam - Phone extraction with Vietnamese patterns
                if not card_info['phone'] and any(char.isdigit() for char in line):  # Nếu chưa có phone và dòng có số
                    phone_patterns = [  # Danh sách pattern điện thoại
                        r'(\+84|0)[0-9\s\-\(\)\.]{8,15}',  # Pattern điện thoại Việt Nam - Vietnamese phone patterns
                        r'[0-9\s\-\(\)\.]{9,15}',          # Pattern điện thoại chung - General phone patterns
                    ]

                    for pattern in phone_patterns:  # Lặp qua từng pattern
                        phones = re.findall(pattern, line)  # Tìm số điện thoại trong dòng
                        if phones:  # Nếu tìm thấy số điện thoại
                            # Làm sạch và validate số điện thoại - Clean and validate phone number
                            phone = phones[0].strip()  # Loại bỏ khoảng trắng
                            digits_only = re.sub(r'[^\d]', '', phone)  # Chỉ giữ lại số
                            if len(digits_only) >= 9:  # Độ dài tối thiểu của số điện thoại - Minimum phone length
                                card_info['phone'] = phone  # Lưu số điện thoại
                                print(f"      ✅ Found phone: {phone}")  # Log số điện thoại tìm thấy
                                break  # Thoát khỏi vòng lặp

                # Trích xuất website - Website extraction
                if not card_info['website']:  # Nếu chưa tìm thấy website
                    website_patterns = [  # Danh sách pattern website
                        r'(https?://)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Pattern URL đầy đủ
                        r'www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Pattern www
                    ]

                    for pattern in website_patterns:  # Lặp qua từng pattern
                        websites = re.findall(pattern, line, re.IGNORECASE)  # Tìm website trong dòng
                        if websites:  # Nếu tìm thấy website
                            card_info['website'] = websites[0]  # Lấy website đầu tiên
                            print(f"      ✅ Found website: {websites[0]}")  # Log website tìm thấy
                            break  # Thoát khỏi vòng lặp

                # Phát hiện tên công ty - Company name detection
                if not card_info['company']:  # Nếu chưa tìm thấy công ty
                    # Danh sách từ khóa chỉ báo công ty
                    company_indicators = ['company', 'corp', 'ltd', 'inc', 'co.', 'llc', 'solution', 'tech', 'software', 'group', 'enterprise']
                    if any(indicator in line_lower for indicator in company_indicators):  # Nếu dòng chứa từ khóa công ty
                        # Bỏ qua nếu rõ ràng không phải tên công ty - Skip if it's clearly not a company name
                        if not any(skip in line_lower for skip in ['email', 'phone', 'website', '@', 'www']):  # Nếu không chứa từ khóa loại trừ
                            card_info['company'] = line  # Lưu tên công ty
                            print(f"      ✅ Found company: {line}")  # Log công ty tìm thấy

                # Phát hiện tên người (thường xuất hiện sớm, 2-4 từ, không có số) - Name detection (usually appears early, 2-4 words, no numbers)
                if not card_info['name'] and len(line.split()) >= 2 and len(line.split()) <= 4:  # Nếu chưa có tên và dòng có 2-4 từ
                    # Kiểm tra xem có giống tên người không - Check if it looks like a person's name
                    if (not any(char.isdigit() for char in line) and  # Không chứa số
                        not '@' in line and  # Không chứa @
                        not any(indicator in line_lower for indicator in ['company', 'corp', 'ltd', 'manager', 'director', 'engineer', 'www', '.com'])):  # Không chứa từ khóa loại trừ
                        card_info['name'] = line  # Lưu tên người
                        print(f"      ✅ Found name: {line}")  # Log tên tìm thấy

                # Phát hiện chức vụ (job titles) - Title detection (job titles)
                if not card_info['title']:  # Nếu chưa tìm thấy chức vụ
                    # Danh sách từ khóa chức vụ
                    title_keywords = ['manager', 'director', 'engineer', 'developer', 'analyst', 'specialist', 'coordinator', 'assistant', 'executive', 'officer', 'lead', 'senior', 'junior', 'head', 'chief', 'president', 'ceo', 'cto', 'cfo']
                    if any(keyword in line_lower for keyword in title_keywords):  # Nếu dòng chứa từ khóa chức vụ
                        if not any(skip in line_lower for skip in ['company', '@', 'www', '.com']):  # Nếu không chứa từ khóa loại trừ
                            card_info['title'] = line  # Lưu chức vụ
                            print(f"      ✅ Found title: {line}")  # Log chức vụ tìm thấy

                # Phát hiện địa chỉ (thường là dòng dài với chỉ báo vị trí) - Address detection (usually longer lines with location indicators)
                if not card_info['address']:  # Nếu chưa tìm thấy địa chỉ
                    # Danh sách từ khóa chỉ báo địa chỉ
                    address_indicators = ['street', 'st.', 'avenue', 'ave', 'road', 'rd', 'district', 'city', 'vietnam', 'vn', 'address']
                    if any(indicator in line_lower for indicator in address_indicators):  # Nếu dòng chứa từ khóa địa chỉ
                        card_info['address'] = line  # Lưu địa chỉ
                        print(f"      ✅ Found address: {line}")  # Log địa chỉ tìm thấy

            print("✅ Smart text parsing completed")  # Log hoàn thành parse text thông minh
            self._print_card_info(card_info)  # In thông tin card đã trích xuất
            return card_info  # Trả về thông tin card

        except Exception as e:
            print(f"⚠️ Smart parsing error: {e}")  # Log lỗi parse thông minh
            return self._get_fallback_card_info()  # Trả về thông tin mặc định

    def _print_card_info(self, card_info):
        """In thông tin card theo định dạng sạch - Print card information in clean format"""
        print(f"   📄 Extracted Card Information")  # Header thông tin đã trích xuất
        print(f"   Name: {card_info['name']}")  # In tên
        print(f"   Title: {card_info['title']}")  # In chức vụ
        print(f"   Company: {card_info['company']}")  # In công ty
        print(f"   Email: {card_info['email']}")  # In email
        print(f"   Phone: {card_info['phone']}")  # In điện thoại
        print(f"   Website: {card_info['website']}")  # In website

    def _get_fallback_card_info(self):
        """Thông tin card dự phòng khi OCR thất bại - Fallback card info when OCR fails"""
        print("⚠️ Using fallback card info - OCR extraction failed")
        return {
            'name': '',  # Để trống thay vì fake data
            'title': '',  # Để trống thay vì fake data
            'company': '',  # Để trống thay vì fake data
            'email': '',  # Để trống thay vì fake data
            'phone': '',  # Để trống thay vì fake data
            'address': '',  # Để trống thay vì fake data
            'website': '',  # Để trống thay vì fake data
            'additional_info': 'OCR extraction failed - please try again with better lighting'  # Thông tin bổ sung
        }
    
    def _encode_image(self, image_path):
        """Mã hóa ảnh thành base64 cho Gemini API với tiền xử lý để OCR tốt hơn - Encode image to base64 for Gemini API with preprocessing for better OCR"""

        try:
            if not Path(image_path).exists():  # Nếu file ảnh không tồn tại
                print(f"❌ Image not found: {image_path}")  # Log không tìm thấy ảnh
                return None  # Trả về None

            # Import PIL để tiền xử lý ảnh - Import PIL for image preprocessing
            from PIL import Image, ImageEnhance, ImageFilter  # Import các class xử lý ảnh
            import io  # Import thư viện IO

            # Mở và tiền xử lý ảnh để OCR tốt hơn - Open and preprocess image for better OCR
            with Image.open(image_path) as img:  # Mở ảnh với context manager
                print(f"📸 Original image size: {img.size}")  # Log kích thước ảnh gốc

                # Chuyển đổi sang RGB nếu cần - Convert to RGB if needed
                if img.mode != 'RGB':  # Nếu không phải chế độ RGB
                    img = img.convert('RGB')  # Chuyển đổi sang RGB

                # Resize để có độ phân giải cao hơn cho OCR tốt hơn (tối đa 3072px) - Resize to higher resolution for better OCR (max 3072px)
                max_size = 3072  # Tăng kích thước tối đa để OCR tốt hơn
                min_size = 1024  # Kích thước tối thiểu để đảm bảo chất lượng

                current_max = max(img.size)
                if current_max < min_size:  # Nếu ảnh quá nhỏ
                    # Phóng to ảnh nhỏ lên ít nhất 1024px - Upscale small images to at least 1024px
                    scale_factor = min_size / current_max  # Tính hệ số phóng to
                    new_size = (int(img.width * scale_factor), int(img.height * scale_factor))  # Kích thước mới
                    img = img.resize(new_size, Image.Resampling.LANCZOS)  # Resize với thuật toán LANCZOS
                    print(f"📈 Upscaled from {current_max}px to: {img.size}")  # Log kích thước sau khi phóng to
                elif current_max < max_size:  # Nếu ảnh nhỏ hơn max nhưng lớn hơn min
                    # Phóng to ảnh để tối ưu OCR - Upscale for optimal OCR
                    scale_factor = max_size / current_max  # Tính hệ số phóng to
                    new_size = (int(img.width * scale_factor), int(img.height * scale_factor))  # Kích thước mới
                    img = img.resize(new_size, Image.Resampling.LANCZOS)  # Resize với thuật toán LANCZOS
                    print(f"📈 Optimized from {current_max}px to: {img.size}")  # Log kích thước sau khi phóng to
                elif current_max > max_size:  # Nếu ảnh lớn hơn kích thước tối đa
                    # Thu nhỏ ảnh lớn - Downscale large images
                    scale_factor = max_size / current_max  # Tính hệ số thu nhỏ
                    new_size = (int(img.width * scale_factor), int(img.height * scale_factor))  # Kích thước mới
                    img = img.resize(new_size, Image.Resampling.LANCZOS)  # Resize với thuật toán LANCZOS
                    print(f"📉 Downscaled from {current_max}px to: {img.size}")  # Log kích thước sau khi thu nhỏ

                # Nâng cao ảnh nâng cao để có độ chính xác OCR tối đa - Advanced image enhancement for maximum OCR accuracy
                print("🔧 Applying advanced image enhancements...")  # Log bắt đầu nâng cao ảnh

                # Bước 1: Tăng độ tương phản mạnh để văn bản rõ ràng - Step 1: Increase contrast significantly for text clarity
                enhancer = ImageEnhance.Contrast(img)  # Tạo enhancer độ tương phản
                img = enhancer.enhance(1.8)  # Tăng độ tương phản 80% - Increased for better OCR

                # Bước 2: Tăng độ sáng vừa phải để cải thiện khả năng đọc - Step 2: Enhance brightness moderately to improve readability
                enhancer = ImageEnhance.Brightness(img)  # Tạo enhancer độ sáng
                img = enhancer.enhance(1.15)  # Tăng độ sáng 15% - Increased slightly

                # Bước 3: Tăng độ sắc nét mạnh cho cạnh văn bản rõ ràng - Step 3: Increase sharpness significantly for crisp text edges
                enhancer = ImageEnhance.Sharpness(img)  # Tạo enhancer độ sắc nét
                img = enhancer.enhance(2.2)  # Tăng độ sắc nét 120% - Increased significantly for OCR

                # Bước 4: Áp dụng unsharp mask để văn bản rõ ràng - Step 4: Apply unsharp mask for text clarity
                img = img.filter(ImageFilter.UnsharpMask(radius=2, percent=200, threshold=2))  # Áp dụng unsharp mask

                # Bước 5: Áp dụng bộ lọc làm sắc nét bổ sung - Step 5: Apply additional sharpening filter
                img = img.filter(ImageFilter.SHARPEN)  # Áp dụng bộ lọc SHARPEN

                # Bước 6: Tăng cường cạnh để nhận dạng văn bản tốt hơn - Step 6: Enhance edges for better text recognition
                img = img.filter(ImageFilter.EDGE_ENHANCE_MORE)  # Áp dụng bộ lọc tăng cường cạnh

                print(f"✨ Advanced image enhancement completed")  # Log hoàn thành nâng cao ảnh
                print(f"   - Contrast: +80% (enhanced for OCR)")  # Log tăng độ tương phản
                print(f"   - Brightness: +15% (optimized)")  # Log tăng độ sáng
                print(f"   - Sharpness: +120% (maximum for text)")  # Log tăng độ sắc nét
                print(f"   - Unsharp mask applied")  # Log áp dụng unsharp mask
                print(f"   - Edge enhancement applied")  # Log áp dụng tăng cường cạnh

                # Chuyển đổi thành bytes - Convert to bytes
                img_buffer = io.BytesIO()  # Tạo buffer IO
                img.save(img_buffer, format='JPEG', quality=95, optimize=True)  # Lưu ảnh vào buffer
                img_buffer.seek(0)  # Đặt con trỏ về đầu buffer

                # Mã hóa thành base64 - Encode to base64
                image_data = base64.b64encode(img_buffer.getvalue()).decode('utf-8')  # Mã hóa base64
                print(f"📦 Image encoded: {len(image_data)} characters")  # Log độ dài dữ liệu đã mã hóa

                return image_data  # Trả về dữ liệu ảnh đã mã hóa

        except Exception as e:
            print(f"❌ Image encoding error: {e}")  # Log lỗi mã hóa ảnh
            # Fallback sang mã hóa đơn giản - Fallback to simple encoding
            try:
                with open(image_path, 'rb') as f:  # Mở file ở chế độ binary
                    image_data = base64.b64encode(f.read()).decode('utf-8')  # Mã hóa base64 đơn giản
                print(f"📦 Fallback encoding: {len(image_data)} characters")  # Log mã hóa fallback
                return image_data  # Trả về dữ liệu đã mã hóa
            except:
                return None  # Trả về None nếu tất cả đều thất bại

def test_gemini_ocr_service():
    """Test dịch vụ Gemini OCR - Test Gemini OCR Service"""
    print("🧪 Testing Gemini OCR Service")  # Log bắt đầu test
    print("=" * 50)  # In dòng phân cách

    ocr = GeminiOCRService()  # Tạo instance OCR service

    # Test với name card - Test with business card
    card_path = "card1.jpg"  # Đường dẫn ảnh test
    if Path(card_path).exists():  # Nếu file test tồn tại
        print(f"\n📄 Testing OCR with: {card_path}")  # Log đang test với file

        result = ocr.extract_text_from_card(card_path)  # Gọi hàm trích xuất text

        print("✅ OCR Result:")  # Log kết quả OCR
        for key, value in result.items():  # Lặp qua từng cặp key-value
            print(f"   {key}: {value}")  # In từng thông tin
    else:
        print("❌ No test card image found")  # Log không tìm thấy ảnh test

if __name__ == "__main__":
    test_gemini_ocr_service()
